#!/usr/bin/env python3
"""
特征选择控制器 - 统一的特征选择工作流程
=====================================

这是整个特征选择系统的主控制器，整合了：
1. 特征库管理
2. 标准化回测引擎  
3. 特征分析器
4. 结果可视化和报告

提供了多种特征选择策略：
- 单特征消融测试
- 智能组合搜索
- 完整自动化选择
- 自定义测试方案

作者: Augment Agent
日期: 2025-09-27
"""

import os
import sys
import warnings
import numpy as np
import pandas as pd
from datetime import datetime
from typing import List, Dict, Optional, Tuple, Any
from pathlib import Path
import json
import time
from concurrent.futures import ThreadPoolExecutor, as_completed
import itertools

# 添加上级目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from config_loader import load_config

from feature_selection_system import (
    FeatureLibrary, StandardizedBacktestEngine, SystemConfig, BacktestResult
)
from feature_analyzer import FeatureAnalyzer, FeatureAnalysisResult

warnings.filterwarnings('ignore')

class FeatureSelectionController:
    """特征选择控制器"""
    
    def __init__(self, config: SystemConfig = None, main_config=None):
        self.config = config or SystemConfig()
        self.main_config = main_config or load_config()
        
        # 初始化组件
        self.feature_library = FeatureLibrary()
        self.backtest_engine = StandardizedBacktestEngine(self.config, self.main_config)
        self.analyzer = FeatureAnalyzer(self.config.significance_level)
        
        # 结果存储
        self.test_results: List[BacktestResult] = []
        self.analysis_results: List[FeatureAnalysisResult] = []
        
        # 创建结果目录
        self.results_dir = Path(self.config.results_dir)
        self.results_dir.mkdir(exist_ok=True)
        
        print("🚀 特征选择系统初始化完成")
        print(f"📊 可用特征: {len(self.feature_library.features)}个")
        print(f"📂 结果目录: {self.results_dir}")
    
    def run_ablation_study(self, additional_features: List[str] = None) -> Dict[str, Any]:
        """运行特征消融研究"""
        print("\n🔬 开始特征消融研究")
        print("=" * 60)
        
        # 连接数据源
        if not self.backtest_engine.connect_mt5():
            return {'error': 'MT5连接失败'}
        
        try:
            # 获取数据
            data = self.backtest_engine.get_data()
            if data.empty:
                return {'error': '数据获取失败'}
            
            print(f"📊 数据: {len(data)}条记录")
            
            # 获取基准特征和测试特征
            baseline_features = self.feature_library.get_baseline_features()
            test_features = additional_features or [
                name for name in self.feature_library.get_feature_names() 
                if name not in baseline_features
            ]
            
            # 计算所有需要的特征
            all_features = list(set(baseline_features + test_features))
            data_with_features = self.feature_library.calculate_features(data, all_features)
            
            # 测试方案
            test_cases = [
                {
                    'name': 'v0基础版 (baseline)',
                    'features': baseline_features
                }
            ]
            
            # 添加单特征测试
            for feature in test_features:
                test_cases.append({
                    'name': f'v0 + {feature}',
                    'features': baseline_features + [feature]
                })
            
            print(f"🧪 测试方案: {len(test_cases)}个")
            
            # 运行测试
            results = []
            for i, test_case in enumerate(test_cases, 1):
                print(f"\n[{i}/{len(test_cases)}] 测试: {test_case['name']}")
                
                result = self.backtest_engine.run_single_backtest(
                    data_with_features, 
                    test_case['features'], 
                    test_case['name']
                )
                
                if result:
                    results.append(result)
                    self.test_results.append(result)
                    print(f"✅ 夏普比率: {result.sharpe_ratio:.2f}")
                else:
                    print("❌ 测试失败")
            
            if not results:
                return {'error': '所有测试均失败'}
            
            # 分析结果
            self.analysis_results = self.analyzer.analyze_feature_contribution(
                baseline_features, results
            )
            
            # 生成报告和可视化
            self._generate_outputs(data_with_features, all_features)
            
            return {
                'total_tests': len(test_cases),
                'successful_tests': len(results),
                'best_feature': self.analysis_results[0].feature_name if self.analysis_results else None,
                'best_improvement': self.analysis_results[0].improvement_pct if self.analysis_results else 0,
                'results_dir': str(self.results_dir)
            }
            
        finally:
            self.backtest_engine.disconnect()
    
    def run_smart_selection(self, max_features: int = None, 
                           search_strategy: str = 'greedy') -> Dict[str, Any]:
        """运行智能特征选择"""
        print("\n🧠 开始智能特征选择")
        print("=" * 60)
        
        max_features = max_features or self.config.max_features
        
        # 连接数据源
        if not self.backtest_engine.connect_mt5():
            return {'error': 'MT5连接失败'}
        
        try:
            # 获取数据
            data = self.backtest_engine.get_data()
            if data.empty:
                return {'error': '数据获取失败'}
            
            # 获取所有特征
            all_features = self.feature_library.get_feature_names()
            data_with_features = self.feature_library.calculate_features(data, all_features)
            
            if search_strategy == 'greedy':
                best_combination = self._greedy_search(data_with_features, max_features)
            elif search_strategy == 'exhaustive':
                best_combination = self._exhaustive_search(data_with_features, max_features)
            else:
                return {'error': f'未知搜索策略: {search_strategy}'}
            
            return best_combination
            
        finally:
            self.backtest_engine.disconnect()
    
    def _greedy_search(self, data: pd.DataFrame, max_features: int) -> Dict[str, Any]:
        """贪心搜索最优特征组合"""
        print("🔍 使用贪心搜索策略...")
        
        baseline_features = self.feature_library.get_baseline_features()
        candidate_features = [
            name for name in self.feature_library.get_feature_names() 
            if name not in baseline_features
        ]
        
        current_features = baseline_features.copy()
        current_result = self.backtest_engine.run_single_backtest(
            data, current_features, "baseline"
        )
        
        if not current_result:
            return {'error': '基准测试失败'}
        
        best_sharpe = current_result.sharpe_ratio
        results = [current_result]
        
        print(f"📊 基准夏普比率: {best_sharpe:.2f}")
        
        # 贪心添加特征
        while len(current_features) < max_features and candidate_features:
            best_addition = None
            best_addition_sharpe = best_sharpe
            
            print(f"\n🔄 当前特征数: {len(current_features)}, 候选特征: {len(candidate_features)}")
            
            # 测试每个候选特征
            for feature in candidate_features:
                test_features = current_features + [feature]
                result = self.backtest_engine.run_single_backtest(
                    data, test_features, f"greedy_+{feature}"
                )
                
                if result and result.sharpe_ratio > best_addition_sharpe:
                    best_addition = feature
                    best_addition_sharpe = result.sharpe_ratio
                    best_result = result
            
            # 如果找到改进，添加特征
            if best_addition and best_addition_sharpe > best_sharpe + self.config.min_improvement_threshold:
                current_features.append(best_addition)
                candidate_features.remove(best_addition)
                best_sharpe = best_addition_sharpe
                results.append(best_result)
                
                print(f"✅ 添加特征: {best_addition}, 新夏普比率: {best_sharpe:.2f}")
            else:
                print("🛑 无更多有效特征，停止搜索")
                break
        
        self.test_results.extend(results)
        
        return {
            'strategy': 'greedy',
            'final_features': current_features,
            'final_sharpe': best_sharpe,
            'improvement_steps': len(results) - 1,
            'total_tests': len(results)
        }
    
    def _exhaustive_search(self, data: pd.DataFrame, max_features: int) -> Dict[str, Any]:
        """穷举搜索最优特征组合 (限制组合数量)"""
        print("🔍 使用穷举搜索策略...")
        
        baseline_features = self.feature_library.get_baseline_features()
        candidate_features = [
            name for name in self.feature_library.get_feature_names() 
            if name not in baseline_features
        ]
        
        best_result = None
        best_combination = baseline_features
        total_combinations = 0
        
        # 限制搜索空间
        max_combinations = min(1000, 2**len(candidate_features))
        
        print(f"🔢 搜索空间: 最多{max_combinations}种组合")
        
        # 测试不同大小的组合
        for size in range(1, min(max_features - len(baseline_features) + 1, len(candidate_features) + 1)):
            print(f"\n🔄 测试 {size} 个额外特征的组合...")
            
            combinations = list(itertools.combinations(candidate_features, size))
            
            # 限制组合数量
            if len(combinations) > max_combinations // (max_features - len(baseline_features)):
                combinations = combinations[:max_combinations // (max_features - len(baseline_features))]
            
            for combo in combinations:
                test_features = baseline_features + list(combo)
                result = self.backtest_engine.run_single_backtest(
                    data, test_features, f"exhaustive_{size}_{total_combinations}"
                )
                
                if result:
                    self.test_results.append(result)
                    if best_result is None or result.sharpe_ratio > best_result.sharpe_ratio:
                        best_result = result
                        best_combination = test_features
                
                total_combinations += 1
                
                if total_combinations >= max_combinations:
                    break
            
            if total_combinations >= max_combinations:
                break
        
        return {
            'strategy': 'exhaustive',
            'final_features': best_combination,
            'final_sharpe': best_result.sharpe_ratio if best_result else 0,
            'total_tests': total_combinations,
            'best_result': best_result
        }
    
    def _generate_outputs(self, data: pd.DataFrame, features: List[str]):
        """生成输出文件"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # 保存测试结果
        results_data = [result.to_dict() for result in self.test_results]
        results_file = self.results_dir / f"test_results_{timestamp}.json"
        with open(results_file, 'w', encoding='utf-8') as f:
            json.dump(results_data, f, ensure_ascii=False, indent=2)
        
        # 特征相关性分析
        correlation_matrix = self.analyzer.analyze_feature_correlation(data, features)
        redundant_features = self.analyzer.identify_redundant_features(correlation_matrix)
        
        # 生成可视化
        if self.config.plot_results and self.analysis_results:
            plot_file = self.results_dir / f"feature_analysis_{timestamp}.png"
            self.analyzer.create_visualization(
                self.analysis_results, correlation_matrix, str(plot_file)
            )
        
        # 生成报告
        report_file = self.results_dir / f"feature_report_{timestamp}.md"
        report = self.analyzer.generate_report(
            self.analysis_results, correlation_matrix, redundant_features, str(report_file)
        )
        
        print(f"\n📁 输出文件:")
        print(f"  - 测试结果: {results_file}")
        print(f"  - 分析报告: {report_file}")
        if self.config.plot_results:
            print(f"  - 可视化图表: {plot_file}")
    
    def get_feature_summary(self) -> pd.DataFrame:
        """获取特征摘要"""
        return self.feature_library.list_features()
    
    def add_custom_feature(self, name: str, calculation_func, description: str = "", 
                          category: str = "custom"):
        """添加自定义特征"""
        self.feature_library.register_feature(name, calculation_func, description, category)
    
    def get_results_summary(self) -> pd.DataFrame:
        """获取结果摘要"""
        if not self.test_results:
            return pd.DataFrame()
        
        data = []
        for result in self.test_results:
            data.append({
                'test_name': result.test_name,
                'feature_count': len(result.feature_set),
                'sharpe_ratio': result.sharpe_ratio,
                'total_return': result.total_return,
                'max_drawdown': result.max_drawdown,
                'win_rate': result.win_rate,
                'execution_time': result.execution_time
            })
        
        df = pd.DataFrame(data)
        return df.sort_values('sharpe_ratio', ascending=False)

if __name__ == "__main__":
    # 测试代码
    controller = FeatureSelectionController()
    print("🎮 特征选择控制器初始化完成")
    
    # 显示可用特征
    features_summary = controller.get_feature_summary()
    print(f"\n📋 可用特征 ({len(features_summary)}个):")
    print(features_summary[['name', 'category', 'description']].to_string(index=False))
