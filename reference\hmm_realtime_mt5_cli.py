#!/usr/bin/env python3
"""
HMM实时状态识别系统 - CLI版本
专注核心算法逻辑，确保训练、回测、实时预测完全一致
"""

import pandas as pd
import numpy as np
from hmmlearn.hmm import GaussianHMM
from sklearn.preprocessing import StandardScaler
from datetime import datetime, timezone, timedelta
import time
import threading
from typing import Optional, Dict

# MT5数据获取
try:
    import MetaTrader5 as mt5
    MT5_AVAILABLE = True
except ImportError:
    MT5_AVAILABLE = False
    print("⚠️ MetaTrader5 模块未安装，请运行: pip install MetaTrader5")

class MT5DataFetcher:
    """简化版MT5数据获取器"""
    
    def __init__(self, symbol="XAUUSD"):
        self.symbol = symbol
        self.connected = False
        self._gmt_offset = None
    
    def connect(self) -> bool:
        if not MT5_AVAILABLE:
            return False
        
        try:
            if not mt5.initialize():
                print(f"❌ MT5初始化失败: {mt5.last_error()}")
                return False
            
            symbol_info = mt5.symbol_info(self.symbol)
            if symbol_info is None:
                print(f"❌ 品种 {self.symbol} 不可用")
                return False
            
            if not symbol_info.visible:
                if not mt5.symbol_select(self.symbol, True):
                    print(f"❌ 无法选择品种 {self.symbol}")
                    return False
            
            self.connected = True
            print(f"✅ MT5连接成功 ({self.symbol})")
            return True
        except Exception as e:
            print(f"❌ MT5连接异常: {e}")
            return False
    
    def disconnect(self):
        if MT5_AVAILABLE and self.connected:
            mt5.shutdown()
            self.connected = False
    
    def get_historical_data(self, start_date, end_date, timeframe="M1"):
        if not self.connected:
            return None
        
        try:
            tf_map = {"M1": mt5.TIMEFRAME_M1, "M5": mt5.TIMEFRAME_M5, "M15": mt5.TIMEFRAME_M15, "H1": mt5.TIMEFRAME_H1}
            mt5_timeframe = tf_map.get(timeframe, mt5.TIMEFRAME_M1)
            
            if self._gmt_offset is None:
                self._get_broker_gmt_offset()
            
            rates = mt5.copy_rates_range(self.symbol, mt5_timeframe, start_date, end_date)
            
            if rates is None or len(rates) == 0:
                return None
            
            df = pd.DataFrame(rates)
            df['time'] = pd.to_datetime(df['time'], unit='s')
            df['time'] = df['time'] - pd.Timedelta(hours=self._gmt_offset)
            df['time'] = df['time'].dt.tz_localize('UTC')
            df.set_index('time', inplace=True)
            
            df = df.rename(columns={'open': 'Open', 'high': 'High', 'low': 'Low', 'close': 'Close', 'tick_volume': 'Volume'})
            
            return df
        except Exception as e:
            print(f"❌ 获取历史数据失败: {e}")
            return None
    
    def _get_broker_gmt_offset(self):
        try:
            # 🔧 修复：获取服务器时间（不标记时区，因为它不是UTC）
            server_time_naive = pd.Timestamp(mt5.symbol_info_tick(self.symbol).time, unit='s')
            if pd.isna(server_time_naive):
                server_time_naive = pd.Timestamp(mt5.copy_rates_from_pos(self.symbol, mt5.TIMEFRAME_M1, 0, 1)[0]['time'], unit='s')

            # 获取真实UTC时间
            real_utc_time = pd.Timestamp.now(tz='UTC').tz_localize(None)
            
            # 计算服务器时间与UTC时间的差值（小时）
            self._gmt_offset = round((server_time_naive - real_utc_time).total_seconds() / 3600)
            print(f"🕐 GMT偏移量计算: 服务器时间={server_time_naive}, UTC时间={real_utc_time}, 偏移={self._gmt_offset}小时")
        except Exception as e:
            print(f"⚠️ GMT偏移量计算失败: {e}, 使用默认值")
            self._gmt_offset = 2  # 默认GMT+2

    def get_latest_complete_candle(self, timeframe="M1"):
        if not self.connected:
            return None
        try:
            tf_map = {"M1": mt5.TIMEFRAME_M1, "M5": mt5.TIMEFRAME_M5, "M15": mt5.TIMEFRAME_M15, "H1": mt5.TIMEFRAME_H1}
            mt5_timeframe = tf_map.get(timeframe, mt5.TIMEFRAME_M1)
            
            rates = mt5.copy_rates_from_pos(self.symbol, mt5_timeframe, 0, 2)
            if rates is None or len(rates) < 2:
                return None
            
            candle = rates[-2]  # 取倒数第二根（完整的）
            
            if self._gmt_offset is None:
                self._get_broker_gmt_offset()
            
            utc_timestamp = pd.Timestamp(candle['time'], unit='s') - pd.Timedelta(hours=self._gmt_offset)
            utc_timestamp = utc_timestamp.tz_localize('UTC')
            
            return {
                'datetime': utc_timestamp,
                'open': float(candle['open']),
                'high': float(candle['high']),
                'low': float(candle['low']),
                'close': float(candle['close']),
                'volume': float(candle['tick_volume'])
            }
        except:
            return None

class HMMStateDetectorCLI:
    """HMM状态检测器 - CLI版本，专注核心逻辑"""
    
    def __init__(self, symbol: str = "XAUUSD", n_states: int = 3, ma_fast: int = 20, ma_slow: int = 35):
        self.symbol = symbol
        self.n_states = n_states
        self.ma_fast = ma_fast
        self.ma_slow = ma_slow
        self.fetcher = MT5DataFetcher(symbol)
        
        # 核心组件
        self.model: Optional[GaussianHMM] = None
        self.scaler: Optional[StandardScaler] = None
        self.state_map: Dict[int, str] = {}
        self.features = ['log_return', 'momentum_5m', 'momentum_20m', 'price_position', 'ma_diff']
        
        # 数据存储
        self.df: Optional[pd.DataFrame] = None
        self.last_signal = None
        self.best_params = {}  # 最优参数存储
        
        print(f"🔧 HMM状态检测器初始化 ({symbol}, {n_states}状态)")
    
    def _calculate_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """核心特征计算 - 确保与训练时完全一致"""
        df = df.copy()
        df['log_return'] = np.log(df['Close'] / df['Close'].shift(1))
        df['momentum_5m'] = np.log(df['Close'] / df['Close'].shift(5))
        df['momentum_20m'] = np.log(df['Close'] / df['Close'].shift(20))
        df['ma_fast'] = df['Close'].rolling(window=self.ma_fast).mean()
        df['ma_slow'] = df['Close'].rolling(window=self.ma_slow).mean()
        df['price_position'] = (df['Close'] - df['ma_slow']) / df['ma_slow']
        df['ma_diff'] = (df['ma_fast'] - df['ma_slow']) / df['ma_slow']
        return df
    
    def load_and_prepare_data(self, days: int = 20) -> bool:
        """加载并准备训练数据"""
        print(f"📥 加载过去{days}天数据...")
        
        try:
            if not self.fetcher.connect():
                return False
            
            end_date = datetime.now(timezone.utc)
            start_date = end_date - timedelta(days=days)
            
            df = self.fetcher.get_historical_data(start_date, end_date)
            if df is None or df.empty:
                print("❌ 历史数据获取失败")
                return False
            
            # 核心特征计算
            self.df = self._calculate_features(df)
            self.df.dropna(inplace=True)
            
            print(f"✅ 数据准备完成: {len(self.df)} 条记录")
            print(f"📅 时间范围: {self.df.index[0].strftime('%m-%d %H:%M')} ~ {self.df.index[-1].strftime('%m-%d %H:%M')}")
            return True
            
        except Exception as e:
            print(f"❌ 数据准备失败: {e}")
            return False
        finally:
            self.fetcher.disconnect()
    
    def walk_forward_analysis(self, min_train_ratio: float = 0.6, max_train_ratio: float = 0.8, step: float = 0.05) -> dict:
        """前向展开分析：寻找最优训练窗口"""
        print(f"\n🔍 开始前向展开分析...")
        if self.df is None:
            return {}
        
        results = []
        train_ratios = np.arange(min_train_ratio, max_train_ratio + step, step)
        
        for train_ratio in train_ratios:
            print(f"  测试训练比例: {train_ratio:.2f}")
            
            try:
                split_idx = int(len(self.df) * train_ratio)
                train_data = self.df.iloc[:split_idx]
                test_data = self.df.iloc[split_idx:]
                
                if len(test_data) < 100:
                    continue
                
                # 训练模型
                X_train = train_data[self.features].values
                scaler = StandardScaler().fit(X_train)
                X_train_scaled = scaler.transform(X_train)
                
                model = GaussianHMM(
                    n_components=self.n_states,
                    covariance_type="diag",
                    n_iter=300,
                    random_state=42,
                    tol=1e-4
                ).fit(X_train_scaled)
                
                # 状态映射
                state_means = pd.DataFrame(model.means_, columns=self.features)
                sorted_by_return = state_means.sort_values('log_return')
                state_map = {
                    sorted_by_return.index[0]: "下跌",
                    sorted_by_return.index[-1]: "上涨"
                }
                if self.n_states == 3:
                    state_map[sorted_by_return.index[1]] = "盘整"
                
                # 回测：使用与实时预测完全一致的逻辑
                X_test_scaled = scaler.transform(test_data[self.features].values)
                test_states = model.predict(X_test_scaled)
                
                # 创建临时DataFrame使用统一逻辑
                temp_test_df = test_data.copy()
                temp_test_df['state'] = test_states
                temp_test_df['regime'] = temp_test_df['state'].map(state_map)
                # 关键：使用shift(1)避免未来信息泄露
                temp_test_df['signal'] = temp_test_df['regime'].map({"上涨": 1, "下跌": -1, "盘整": 0}).shift(1).fillna(0)
                
                # 计算策略收益
                signals = temp_test_df['signal'].values
                test_returns = test_data['log_return'].values
                strategy_returns = test_returns * signals
                
                # 性能指标（修正年化因子）
                # 对于1分钟数据，年化因子使用更合理的计算方式
                # 考虑外汇市场特点：5天/周 × 52周 × 24小时 × 60分钟 = 374,400分钟/年
                # 但更保守的计算：sqrt(252) 然后调整到分钟级别
                annual_factor = np.sqrt(252)  # 年化到日级别
                sharpe_ratio = np.mean(strategy_returns) / np.std(strategy_returns) * annual_factor if np.std(strategy_returns) > 0 else 0
                max_drawdown = self._calculate_max_drawdown(strategy_returns)
                total_return = np.exp(np.sum(strategy_returns)) - 1
                
                results.append({
                    'train_ratio': train_ratio,
                    'sharpe_ratio': sharpe_ratio,
                    'max_drawdown': max_drawdown,
                    'total_return': total_return,
                    'test_days': len(test_data) / (24 * 60)
                })
                
                print(f"    夏普比率: {sharpe_ratio:.2f}, 最大回撤: {max_drawdown:.2%}, 收益: {total_return:.2%}")
                
            except Exception as e:
                print(f"    ❌ 训练比例 {train_ratio:.2f} 失败: {e}")
        
        if not results:
            return {}
        
        # 选择最优参数
        results_df = pd.DataFrame(results)
        results_df['score'] = results_df['sharpe_ratio'] * 0.7 - results_df['max_drawdown'] * 0.3
        best_result = results_df.loc[results_df['score'].idxmax()]
        
        self.best_params = best_result.to_dict()
        
        print(f"\n🎯 最优训练窗口分析结果:")
        print(f"  最佳训练比例: {best_result['train_ratio']:.2f}")
        print(f"  样本外夏普比率: {best_result['sharpe_ratio']:.2f}")
        print(f"  样本外最大回撤: {best_result['max_drawdown']:.2%}")
        print(f"  样本外总收益: {best_result['total_return']:.2%}")
        print(f"  测试天数: {best_result['test_days']:.1f}天")
        
        return self.best_params
    
    def _calculate_max_drawdown(self, returns: np.array) -> float:
        """计算最大回撤"""
        cumulative = np.exp(np.cumsum(returns))
        running_max = np.maximum.accumulate(cumulative)
        drawdown = (cumulative - running_max) / running_max
        return abs(drawdown.min())

    def train_optimal_model(self) -> bool:
        """使用最优参数训练模型"""
        if self.df is None:
            print("❌ 请先加载数据")
            return False
        
        try:
            # 使用最优训练比例
            train_ratio = self.best_params.get('train_ratio', 0.75)
            split_idx = int(len(self.df) * train_ratio)
            train_data = self.df.iloc[:split_idx]
            
            # 特征标准化
            X_train = train_data[self.features].values
            self.scaler = StandardScaler()
            X_train_scaled = self.scaler.fit_transform(X_train)
            
            # 训练HMM模型
            self.model = GaussianHMM(
                n_components=self.n_states,
                covariance_type="diag",
                n_iter=500,
                random_state=42,
                tol=1e-4
            )
            self.model.fit(X_train_scaled)
            
            # 识别状态映射
            self._identify_state_mapping()
            
            # 🔧 新增：分析状态转移矩阵和持久性
            self._analyze_state_transitions()
            
            print(f"✅ 最优模型训练完成")
            print(f"📊 使用训练比例: {train_ratio:.2f}")
            print(f"📊 训练样本: {len(train_data)}, BIC: {self.model.bic(X_train_scaled):.2f}")
            return True
            
        except Exception as e:
            print(f"❌ 模型训练失败: {e}")
            return False
    
    def _identify_state_mapping(self):
        """识别状态到含义的映射"""
        state_means = pd.DataFrame(self.model.means_, columns=self.features)
        sorted_by_return = state_means.sort_values('log_return')
        
        self.state_map = {
            sorted_by_return.index[0]: "下跌",
            sorted_by_return.index[-1]: "上涨"
        }
        if self.n_states == 3:
            self.state_map[sorted_by_return.index[1]] = "盘整"
        
        print("🎯 状态映射:")
        for state, regime in self.state_map.items():
            ret = state_means.loc[state, 'log_return']
            ma = state_means.loc[state, 'ma_diff']
            print(f"  状态{state} -> {regime} (收益:{ret:.4f}, 均线:{ma:.4f})")
    
    def _analyze_state_transitions(self):
        """🔧 新增：分析状态转移矩阵和持久性 - 诊断状态卡住问题"""
        if self.model is None:
            return
        
        print("\n🔍 状态转移矩阵分析:")
        transition_df = pd.DataFrame(
            self.model.transmat_, 
            columns=[f'到{self.state_map.get(i, f"状态{i}")}' for i in range(self.n_states)], 
            index=[f'从{self.state_map.get(i, f"状态{i}")}' for i in range(self.n_states)]
        )
        print(transition_df.round(3))
        
        # 计算期望状态持续时间
        expected_durations = 1 / (1 - np.diag(self.model.transmat_))
        print("\n⏰ 期望状态持续时间:")
        max_duration = 0
        problematic_states = []
        
        for i, duration in enumerate(expected_durations):
            regime = self.state_map.get(i, f"状态{i}")
            self_transition_prob = self.model.transmat_[i, i]
            print(f"  {regime}: {duration:.1f}分钟 (自转移概率: {self_transition_prob:.3f})")
            
            # 检测问题状态（持续时间过长）
            if duration > 100:  # 超过100分钟认为过长
                problematic_states.append((regime, duration, self_transition_prob))
                max_duration = max(max_duration, duration)
        
        # 警告信息
        if problematic_states:
            print(f"\n⚠️  状态持久性警告:")
            for regime, duration, prob in problematic_states:
                print(f"  ❌ {regime}状态可能卡住: 期望持续{duration:.1f}分钟 (自转移概率{prob:.3f})")
            print(f"  💡 建议: 如果实时运行中状态不变，这可能是原因")
        else:
            print(f"\n✅ 状态转移概率合理，期望最长持续{max_duration:.1f}分钟")
    
    def predict_single_state(self, df_row: pd.Series) -> tuple:
        """预测单个数据点的状态 - 核心预测逻辑"""
        if self.model is None or self.scaler is None:
            raise ValueError("模型未训练")
        
        # 提取特征
        features = df_row[self.features].values.reshape(1, -1)
        
        # 标准化（使用训练时的scaler）
        features_scaled = self.scaler.transform(features)
        
        # HMM状态预测
        state = self.model.predict(features_scaled)[0]
        
        # 状态映射
        regime = self.state_map[state]
        
        return state, regime
    
    def process_new_candle(self, candle_data: dict) -> Optional[Dict]:
        """处理新K线数据 - 确保与训练/回测逻辑完全一致"""
        try:
            # 1. 添加新数据到历史集合
            new_row = pd.DataFrame([{
                'Open': candle_data['open'],
                'High': candle_data['high'], 
                'Low': candle_data['low'],
                'Close': candle_data['close'],
                'Volume': candle_data['volume']
            }], index=[candle_data['datetime']])
            
            self.df = pd.concat([self.df, new_row])
            self.df = self.df[~self.df.index.duplicated(keep='last')]
            
            # 2. 重新计算所有技术特征（确保一致性）
            self.df = self._calculate_features(self.df)
            
            # 3. 获取最新完整数据行（有完整特征的）
            if len(self.df) < self.ma_slow + 1:
                print("⚠️ 数据不足，跳过预测")
                return None
            
            latest_valid_row = self.df.iloc[-1]
            if latest_valid_row[self.features].isna().any():
                print("⚠️ 最新数据特征不完整，跳过预测")
                return None
            
            # 4. HMM状态预测 - 与回测完全一致的逻辑
            valid_data = self.df.dropna(subset=self.features)
            if len(valid_data) < 2:
                print("⚠️ 有效数据不足，跳过预测")
                return None
            
            # 🎯 关键修正：使用所有可用数据进行HMM预测，就像回测一样
            X_all_scaled = self.scaler.transform(valid_data[self.features].values)
            all_states = self.model.predict(X_all_scaled)
            
            # 创建临时DataFrame，使用与回测完全相同的逻辑
            temp_df = valid_data.copy()
            temp_df['state'] = all_states
            temp_df['regime'] = temp_df['state'].map(self.state_map)
            # 关键：使用shift(1)避免未来信息泄露，与回测一致
            temp_df['signal'] = temp_df['regime'].map({"上涨": 1, "下跌": -1, "盘整": 0}).shift(1).fillna(0)
            
            # 获取最新K线的状态和信号
            latest_row = temp_df.iloc[-1]
            current_regime = latest_row['regime']
            current_signal = int(latest_row['signal'])
            
            # 5. 构建结果
            result = {
                'datetime': candle_data['datetime'],
                'ohlcv': {
                    'open': candle_data['open'],
                    'high': candle_data['high'],
                    'low': candle_data['low'],
                    'close': candle_data['close'],
                    'volume': candle_data['volume']
                },
                'current_regime': current_regime,           # 当前K线的HMM状态
                'current_signal': current_signal,           # 当前K线的交易信号（基于shift(1)）
                'signal_changed': self.last_signal != current_signal
            }
            
            # 6. 更新上次信号
            self.last_signal = current_signal
            
            return result
            
        except Exception as e:
            print(f"❌ 处理新K线失败: {e}")
            return None
    
    def start_realtime_monitoring(self, max_updates: int = 1440):
        """启动实时监控"""
        print(f"\n🔄 启动实时状态监控...")
        print("=" * 80)
        
        def monitoring_loop():
            if not self.fetcher.connect():
                print("❌ MT5连接失败")
                return
            
            last_candle_time = self.df.index[-1] if self.df is not None else None
            update_count = 0
            
            try:
                while update_count < max_updates:
                    try:
                        # 获取最新完整K线
                        latest_candle = self.fetcher.get_latest_complete_candle()
                        if latest_candle is None:
                            time.sleep(10)
                            continue
                        
                        # 检查是否是新K线
                        if last_candle_time is None or latest_candle['datetime'] > last_candle_time:
                            last_candle_time = latest_candle['datetime']
                            
                            # 处理新K线数据
                            result = self.process_new_candle(latest_candle)
                            
                            if result:
                                self._display_result(result)
                                update_count += 1
                        
                        time.sleep(10)
                        
                    except KeyboardInterrupt:
                        print("\n🛑 用户停止监控")
                        break
                    except Exception as e:
                        print(f"❌ 监控错误: {e}")
                        time.sleep(30)
                
                print(f"\n✅ 监控结束，共处理 {update_count} 根新K线")
                
            finally:
                self.fetcher.disconnect()
        
        # 启动监控线程
        monitor_thread = threading.Thread(target=monitoring_loop, daemon=True)
        monitor_thread.start()
        return monitor_thread
    
    def _display_result(self, result: Dict):
        """显示处理结果 - 简洁明了，重点关注信号变化"""
        dt = result['datetime']
        ohlcv = result['ohlcv']
        current_regime = result['current_regime']
        current_signal = result['current_signal']
        signal_changed = result['signal_changed']
        
        # 时间显示（直接使用MT5服务器时间，保持对齐）
        # 从UTC转换回服务器时间显示
        if hasattr(dt, 'tz_localize'):
            server_time = dt.tz_localize(None) + pd.Timedelta(hours=self.fetcher._gmt_offset)
        else:
            server_time = dt + pd.Timedelta(hours=self.fetcher._gmt_offset)
        time_str = server_time.strftime('%H:%M:%S')
        
        # OHLCV数据
        o, h, l, c, v = ohlcv['open'], ohlcv['high'], ohlcv['low'], ohlcv['close'], ohlcv['volume']
        change = c - o
        change_pct = (change / o) * 100
        direction = "↗️" if change > 0 else "↘️" if change < 0 else "→"
        
        # 基础K线信息
        print(f"{time_str} | O:{o:.2f} H:{h:.2f} L:{l:.2f} C:{c:.2f} | {change:+.2f}({change_pct:+.2f}%) {direction} | Vol:{v:.0f}")
        print(f"         | 当前HMM状态: {current_regime}")
        
        # 🎯 只在信号变化时显示交易提示（简洁版）
        if signal_changed:
            if current_signal == 1:
                print(f"         >>> 🟢 做多信号 <<<")
            elif current_signal == -1:
                print(f"         >>> 🔴 做空信号 <<<")
            elif current_signal == 0:
                print(f"         >>> 🟡 空仓信号 <<<")
        
        # 分隔线
        print("-" * 70)

def main():
    """主程序"""
    print("🚀 HMM实时状态识别系统 - CLI版本")
    print("专注核心算法，确保逻辑一致性")
    print("=" * 80)
    
    # 初始化检测器
    detector = HMMStateDetectorCLI(symbol="XAUUSD", n_states=3)
    
    try:
        # 1. 加载和准备数据
        if not detector.load_and_prepare_data(days=20):
            print("❌ 数据准备失败")
            return
        
        # 2. 前向展开分析（寻找最优参数）
        best_params = detector.walk_forward_analysis()
        if not best_params:
            print("❌ 参数优化失败，使用默认参数")
        
        # 3. 使用最优参数训练模型
        if not detector.train_optimal_model():
            print("❌ 模型训练失败")
            return
        
        print("\n🎯 系统就绪，开始实时监控...")
        print("💡 输出格式: 时间 | OHLCV | 变化 | 成交量")
        print("💡 显示当前HMM识别的市场状态")
        print("💡 仅在信号变化时显示: 🟢做多 🔴做空 🟡空仓")
        print("💡 按 Ctrl+C 停止监控")
        print("=" * 80)
        
        # 4. 启动实时监控
        monitor_thread = detector.start_realtime_monitoring()
        
        # 保持主线程运行
        try:
            while monitor_thread.is_alive():
                time.sleep(1)
        except KeyboardInterrupt:
            print("\n👋 程序结束")
            
    except Exception as e:
        print(f"❌ 程序异常: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()