#!/usr/bin/env python3
"""
系统测试脚本 - 验证特征选择系统的完整性
=====================================

这个脚本用于测试新的特征选择系统是否正常工作，包括：
1. 组件初始化测试
2. 特征库功能测试
3. 回测引擎测试
4. 分析器功能测试
5. 集成测试

作者: Augment Agent
日期: 2025-09-27
"""

import sys
import os
import traceback
import numpy as np
import pandas as pd
from datetime import datetime, timedelta

# 添加路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_imports():
    """测试模块导入"""
    print("🔧 测试模块导入...")
    
    try:
        from feature_selection_system import FeatureLibrary, SystemConfig, StandardizedBacktestEngine
        from feature_analyzer import FeatureAnalyzer
        from feature_selection_controller import FeatureSelectionController
        print("✅ 核心模块导入成功")
        return True
    except Exception as e:
        print(f"❌ 模块导入失败: {e}")
        traceback.print_exc()
        return False

def test_feature_library():
    """测试特征库"""
    print("\n📚 测试特征库...")
    
    try:
        from feature_selection_system import FeatureLibrary
        
        # 初始化特征库
        library = FeatureLibrary()
        
        # 测试基本功能
        features = library.get_feature_names()
        baseline = library.get_baseline_features()
        categories = library.categories
        
        print(f"✅ 特征库初始化成功")
        print(f"   总特征数: {len(features)}")
        print(f"   基准特征: {len(baseline)}")
        print(f"   特征分类: {list(categories.keys())}")
        
        # 测试特征列表
        features_df = library.list_features()
        print(f"   特征列表生成: {len(features_df)}行")
        
        # 测试自定义特征添加
        def test_feature(df):
            return df['Close'].pct_change().shift(1)
        
        library.register_feature("test_feature", test_feature, "测试特征", "test")
        
        updated_features = library.get_feature_names()
        print(f"   添加自定义特征后: {len(updated_features)}个特征")
        
        return True
        
    except Exception as e:
        print(f"❌ 特征库测试失败: {e}")
        traceback.print_exc()
        return False

def test_feature_calculation():
    """测试特征计算"""
    print("\n🧮 测试特征计算...")
    
    try:
        from feature_selection_system import FeatureLibrary
        
        # 创建模拟数据
        dates = pd.date_range('2024-01-01', periods=1000, freq='T')
        np.random.seed(42)
        
        # 模拟价格数据 (随机游走)
        returns = np.random.normal(0, 0.001, 1000)
        prices = 2000 * np.exp(np.cumsum(returns))
        
        df = pd.DataFrame({
            'Open': prices + np.random.normal(0, 0.1, 1000),
            'High': prices + np.abs(np.random.normal(0, 0.5, 1000)),
            'Low': prices - np.abs(np.random.normal(0, 0.5, 1000)),
            'Close': prices,
            'TickVolume': np.random.randint(100, 1000, 1000),
            'Spread': np.random.uniform(0.1, 0.5, 1000),
            'RealVolume': np.random.randint(1000, 10000, 1000)
        }, index=dates)
        
        # 初始化特征库并计算特征
        library = FeatureLibrary()
        baseline_features = library.get_baseline_features()
        
        result_df = library.calculate_features(df, baseline_features)
        
        print(f"✅ 特征计算成功")
        print(f"   原始数据: {df.shape}")
        print(f"   计算后数据: {result_df.shape}")
        print(f"   新增列数: {result_df.shape[1] - df.shape[1]}")
        
        # 检查特征是否正确计算
        for feature in baseline_features:
            if feature in result_df.columns:
                non_null_count = result_df[feature].notna().sum()
                print(f"   {feature}: {non_null_count}个有效值")
            else:
                print(f"   ❌ {feature}: 未找到")
        
        return True
        
    except Exception as e:
        print(f"❌ 特征计算测试失败: {e}")
        traceback.print_exc()
        return False

def test_analyzer():
    """测试分析器"""
    print("\n📊 测试分析器...")
    
    try:
        from feature_analyzer import FeatureAnalyzer, FeatureAnalysisResult
        from feature_selection_system import BacktestResult
        
        # 初始化分析器
        analyzer = FeatureAnalyzer()
        
        # 创建模拟回测结果
        baseline_result = BacktestResult(
            feature_set=['feature_log_return', 'momentum_5m', 'momentum_20m', 'price_position', 'ma_diff'],
            test_name='baseline',
            sharpe_ratio=2.15,
            total_return=5.2,
            max_drawdown=3.1,
            win_rate=55.2,
            total_trades=45,
            best_train_ratio=0.75,
            execution_time=12.3
        )
        
        test_result = BacktestResult(
            feature_set=['feature_log_return', 'momentum_5m', 'momentum_20m', 'price_position', 'ma_diff', 'rsi'],
            test_name='baseline + rsi',
            sharpe_ratio=2.48,
            total_return=6.1,
            max_drawdown=2.8,
            win_rate=58.1,
            total_trades=42,
            best_train_ratio=0.7,
            execution_time=14.1
        )
        
        # 测试贡献度分析
        baseline_features = ['feature_log_return', 'momentum_5m', 'momentum_20m', 'price_position', 'ma_diff']
        test_results = [baseline_result, test_result]
        
        analysis_results = analyzer.analyze_feature_contribution(baseline_features, test_results)
        
        print(f"✅ 分析器测试成功")
        print(f"   分析结果数: {len(analysis_results)}")
        
        if analysis_results:
            result = analysis_results[0]
            print(f"   特征: {result.feature_name}")
            print(f"   改进: {result.improvement_pct:.2f}%")
            print(f"   显著性: {'是' if result.is_significant else '否'}")
        
        # 测试排名生成
        ranking_df = analyzer.generate_feature_ranking(analysis_results)
        print(f"   排名表生成: {len(ranking_df)}行")
        
        return True
        
    except Exception as e:
        print(f"❌ 分析器测试失败: {e}")
        traceback.print_exc()
        return False

def test_controller():
    """测试控制器 (不连接MT5)"""
    print("\n🎮 测试控制器...")
    
    try:
        from feature_selection_controller import FeatureSelectionController
        from feature_selection_system import SystemConfig
        
        # 创建配置
        config = SystemConfig(
            lookback_days=5,  # 减少数据量用于测试
            plot_results=False  # 不生成图表
        )
        
        # 初始化控制器
        controller = FeatureSelectionController(config)
        
        print(f"✅ 控制器初始化成功")
        
        # 测试特征摘要
        features_summary = controller.get_feature_summary()
        print(f"   特征摘要: {len(features_summary)}个特征")
        
        # 测试自定义特征添加
        def simple_feature(df):
            return df['Close'].rolling(5).mean().shift(1)
        
        controller.add_custom_feature("test_ma5", simple_feature, "5期移动平均", "test")
        
        updated_summary = controller.get_feature_summary()
        print(f"   添加特征后: {len(updated_summary)}个特征")
        
        # 测试结果摘要 (空结果)
        results_summary = controller.get_results_summary()
        print(f"   结果摘要: {len(results_summary)}行")
        
        return True
        
    except Exception as e:
        print(f"❌ 控制器测试失败: {e}")
        traceback.print_exc()
        return False

def test_command_line_interface():
    """测试命令行接口"""
    print("\n💻 测试命令行接口...")
    
    try:
        # 测试帮助信息
        import subprocess
        import sys
        
        # 测试 --help
        result = subprocess.run([
            sys.executable, 'run_feature_selection.py', '--help'
        ], capture_output=True, text=True, cwd='backtest')
        
        if result.returncode == 0:
            print("✅ 帮助信息正常")
        else:
            print(f"❌ 帮助信息失败: {result.stderr}")
            return False
        
        # 测试 --list-features
        result = subprocess.run([
            sys.executable, 'run_feature_selection.py', '--list-features'
        ], capture_output=True, text=True, cwd='backtest')
        
        if result.returncode == 0:
            print("✅ 特征列表正常")
            # 检查输出是否包含预期内容
            if 'feature_log_return' in result.stdout:
                print("   包含基准特征")
            else:
                print("   ⚠️ 未找到基准特征")
        else:
            print(f"❌ 特征列表失败: {result.stderr}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 命令行接口测试失败: {e}")
        traceback.print_exc()
        return False

def test_integration():
    """测试系统集成"""
    print("\n🔗 测试系统集成...")
    
    try:
        # 测试统一测试器
        from unified_feature_tester import compare_all_methods, create_migration_guide
        
        print("✅ 统一测试器导入成功")
        
        # 测试对比功能 (不实际运行)
        print("   对比功能可用")
        
        # 测试迁移指南 (不实际运行)
        print("   迁移指南可用")
        
        return True
        
    except Exception as e:
        print(f"❌ 系统集成测试失败: {e}")
        traceback.print_exc()
        return False

def run_all_tests():
    """运行所有测试"""
    print("🧪 系统完整性测试")
    print("=" * 60)
    
    tests = [
        ("模块导入", test_imports),
        ("特征库", test_feature_library),
        ("特征计算", test_feature_calculation),
        ("分析器", test_analyzer),
        ("控制器", test_controller),
        ("命令行接口", test_command_line_interface),
        ("系统集成", test_integration)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"❌ {test_name}测试异常: {e}")
            results[test_name] = False
    
    # 显示测试结果
    print("\n" + "=" * 60)
    print("📊 测试结果汇总")
    print("=" * 60)
    
    passed = 0
    total = len(tests)
    
    for test_name, success in results.items():
        status = "✅ 通过" if success else "❌ 失败"
        print(f"{test_name:<12} {status}")
        if success:
            passed += 1
    
    print(f"\n📈 总体结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试都通过！系统可以正常使用。")
        print("\n🚀 下一步:")
        print("1. 运行演示: python demo_feature_selection.py")
        print("2. 查看特征: python run_feature_selection.py --list-features")
        print("3. 开始测试: python run_feature_selection.py --mode ablation")
    elif passed > total // 2:
        print("⚠️ 大部分测试通过，系统基本可用。")
        print("请检查失败的测试项目。")
    else:
        print("❌ 多数测试失败，请检查环境配置。")
        print("确保所有依赖包已正确安装。")
    
    return passed == total

if __name__ == "__main__":
    success = run_all_tests()
    exit(0 if success else 1)
