#!/usr/bin/env python3
"""
特征消融测试 - 逐个测试v1新增特征的效果
=====================================

测试策略：
1. v0基础版 (baseline: 5个特征)
2. v0 + RSI (6个特征)
3. v0 + ATR波动率 (6个特征)
4. v0 + 动量加速度 (6个特征)
5. v0 + MACD强度 (6个特征)
6. 最优组合测试

目标：找出真正有效的特征组合
"""

import os
import sys
import warnings
import numpy as np
import pandas as pd
from datetime import datetime, timedelta, timezone
from typing import List, Dict, Optional, Tuple
from dataclasses import dataclass

# 添加上级目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from config_loader import load_config

# 抑制警告
warnings.filterwarnings('ignore', category=FutureWarning)
warnings.filterwarnings('ignore', category=UserWarning)

try:
    import MetaTrader5 as mt5
    from hmmlearn.hmm import GaussianHMM
    from sklearn.preprocessing import StandardScaler
    HMM_AVAILABLE = True
except ImportError as e:
    print(f"⚠️ 导入失败: {e}")
    HMM_AVAILABLE = False

@dataclass
class TestConfig:
    """测试配置"""
    symbol: str = "XAUUSD"
    timeframe: str = "M1"
    lookback_days: int = 20
    n_states: int = 3
    n_iter: int = 500
    covariance_type: str = "diag"
    random_state: int = 42

class FeatureAblationTester:
    """特征消融测试器"""

    def __init__(self, main_config):
        self.main_config = main_config
        self.connected = False

    def connect_mt5(self) -> bool:
        """连接MT5"""
        if not HMM_AVAILABLE:
            return False

        if not mt5.initialize(
            path=self.main_config.mt5_path,
            login=self.main_config.login_id,
            password=self.main_config.password,
            server=self.main_config.server_name,
            timeout=self.main_config.timeout
        ):
            print(f"❌ MT5初始化失败: {mt5.last_error()}")
            return False

        self.connected = True
        account_info = mt5.account_info()
        print(f"✅ MT5连接成功 - 账户: {account_info.login}")
        return True

    def get_data(self, config: TestConfig) -> pd.DataFrame:
        """获取数据"""
        if not self.connected:
            return pd.DataFrame()

        end_date = datetime.now(timezone.utc)
        start_date = end_date - timedelta(days=config.lookback_days)

        rates = mt5.copy_rates_range(
            config.symbol,
            getattr(mt5, f"TIMEFRAME_{config.timeframe}"),
            start_date,
            end_date
        )

        if rates is None or len(rates) == 0:
            return pd.DataFrame()

        df = pd.DataFrame(rates)
        df['time'] = pd.to_datetime(df['time'], unit='s')
        df.set_index('time', inplace=True)
        df.columns = ['Open', 'High', 'Low', 'Close', 'TickVolume', 'Spread', 'RealVolume']
        return df

    def calculate_v0_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """计算v0基础特征"""
        df = df.copy()

        # 基础收益率
        df['log_return'] = np.log(df['Close'] / df['Close'].shift(1))

        # v0最优特征组合
        df['feature_log_return'] = df['log_return'].shift(1)
        df['momentum_5m'] = (df['Close'] / df['Close'].shift(5) - 1).shift(1)
        df['momentum_20m'] = (df['Close'] / df['Close'].shift(20) - 1).shift(1)
        df['price_position'] = ((df['Close'] - df['Low'].rolling(20).min()) /
                               (df['High'].rolling(20).max() - df['Low'].rolling(20).min())).shift(1)
        df['ma_fast'] = df['Close'].rolling(window=20).mean()
        df['ma_slow'] = df['Close'].rolling(window=35).mean()
        df['ma_diff'] = ((df['ma_fast'] - df['ma_slow']) / df['ma_slow']).shift(1)

        return df

    def calculate_additional_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """计算额外特征"""
        df = df.copy()

        # RSI
        delta = df['Close'].diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
        rs = gain / loss
        df['rsi'] = (100 - (100 / (1 + rs))).shift(1)

        # ATR波动率
        high_low = df['High'] - df['Low']
        high_close = np.abs(df['High'] - df['Close'].shift(1))
        low_close = np.abs(df['Low'] - df['Close'].shift(1))
        tr = np.maximum(high_low, np.maximum(high_close, low_close))
        atr = tr.rolling(window=14).mean()
        df['volatility'] = (atr / df['Close']).shift(1)

        # 动量加速度
        momentum_10 = (df['Close'] / df['Close'].shift(10) - 1)
        df['momentum_acceleration'] = (momentum_10 - momentum_10.shift(5)).shift(1)

        # MACD强度
        ema_12 = df['Close'].ewm(span=12).mean()
        ema_26 = df['Close'].ewm(span=26).mean()
        macd = ema_12 - ema_26
        macd_signal = macd.ewm(span=9).mean()
        df['macd_strength'] = ((macd - macd_signal) / df['Close']).shift(1)

        return df

    def calculate_candlestick_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """计算K线形态特征 - 科学设计避免前视偏差"""
        df = df.copy()

        # 1. 实体大小特征 (相对于ATR标准化)
        high_low = df['High'] - df['Low']
        high_close = np.abs(df['High'] - df['Close'].shift(1))
        low_close = np.abs(df['Low'] - df['Close'].shift(1))
        tr = np.maximum(high_low, np.maximum(high_close, low_close))
        atr_20 = tr.rolling(window=20).mean()

        body_size = np.abs(df['Close'] - df['Open'])
        df['body_ratio'] = (body_size / atr_20).shift(1)  # 实体相对ATR大小

        # 2. 上下影线特征
        upper_shadow = df['High'] - np.maximum(df['Open'], df['Close'])
        lower_shadow = np.minimum(df['Open'], df['Close']) - df['Low']
        df['shadow_ratio'] = ((upper_shadow + lower_shadow) / atr_20).shift(1)  # 影线相对ATR

        # 3. 连续K线形态 - Gap特征
        prev_close = df['Close'].shift(1)
        gap_up = (df['Low'] > prev_close).astype(int)  # 向上跳空
        gap_down = (df['High'] < prev_close).astype(int)  # 向下跳空
        df['gap_pattern'] = (gap_up - gap_down).shift(1)  # +1跳空向上, -1跳空向下, 0无跳空

        # 4. 反转形态 - Doji检测
        # Doji: 开盘价接近收盘价，且有较长上下影线
        is_doji = (body_size / atr_20 < 0.1) & ((upper_shadow + lower_shadow) / atr_20 > 0.3)
        df['doji_pattern'] = is_doji.astype(float).shift(1)

        # 5. 趋势延续形态 - 连续同色K线
        is_green = (df['Close'] > df['Open']).astype(int)  # 1为阳线, 0为阴线
        consecutive_green = is_green.rolling(3).sum()  # 连续3根阳线
        consecutive_red = (1 - is_green).rolling(3).sum()  # 连续3根阴线
        df['trend_continuation'] = ((consecutive_green == 3).astype(int) -
                                   (consecutive_red == 3).astype(int)).shift(1)

        return df

    def run_single_test(self, data: pd.DataFrame, features: List[str], test_name: str) -> Dict:
        """运行单个特征组合测试"""
        print(f"\n🧪 测试: {test_name}")
        print(f"特征数量: {len(features)}")
        print(f"特征列表: {features}")

        try:
            # 清理数据
            clean_data = data[features + ['log_return']].dropna()
            if len(clean_data) < 2000:
                return {'error': f'数据不足: {len(clean_data)}'}

            # 前向分析
            best_sharpe = float('-inf')
            best_ratio = 0

            for train_ratio in np.arange(0.6, 0.85, 0.05):
                split_idx = int(len(clean_data) * train_ratio)
                train_data = clean_data.iloc[:split_idx]
                test_data = clean_data.iloc[split_idx:]

                if len(test_data) < 100:
                    continue

                # 训练HMM
                X_train = train_data[features].values
                scaler = StandardScaler().fit(X_train)
                X_train_scaled = scaler.transform(X_train)

                model = GaussianHMM(n_components=3, covariance_type='diag', random_state=42, n_iter=500)
                model.fit(X_train_scaled)

                # 状态映射
                states = model.predict(X_train_scaled)
                state_returns = {}
                for state in range(3):
                    mask = states == state
                    if np.sum(mask) > 0:
                        state_returns[state] = np.mean(train_data['feature_log_return'].iloc[mask])

                sorted_states = sorted(state_returns.items(), key=lambda x: x[1])
                if len(sorted_states) >= 3:
                    state_map = {
                        sorted_states[0][0]: "下跌",
                        sorted_states[1][0]: "盘整",
                        sorted_states[2][0]: "上涨"
                    }
                else:
                    continue

                # 测试预测
                X_test = test_data[features].values
                X_test_scaled = scaler.transform(X_test)
                test_states = model.predict(X_test_scaled)

                test_signals = pd.Series(test_states).map({
                    k: 1 if v == "上涨" else (-1 if v == "下跌" else 0)
                    for k, v in state_map.items()
                }).shift(1).fillna(0).values

                # 计算夏普比率
                test_returns = test_data['log_return'].values
                strategy_returns = test_returns * test_signals[:len(test_returns)]

                if len(strategy_returns) > 0 and np.std(strategy_returns) > 0:
                    signal_changes = np.diff(test_signals) != 0
                    if np.sum(signal_changes) > 0:
                        avg_holding = len(test_signals) / np.sum(signal_changes)
                        trades_per_year = (252 * 23 * 60) / avg_holding
                        sharpe = np.mean(strategy_returns) / np.std(strategy_returns) * np.sqrt(trades_per_year)
                    else:
                        sharpe = np.mean(strategy_returns) / np.std(strategy_returns) * np.sqrt(252)

                    if sharpe > best_sharpe:
                        best_sharpe = sharpe
                        best_ratio = train_ratio

                        # 计算其他指标
                        cumulative_returns = np.cumsum(strategy_returns)
                        peak = np.maximum.accumulate(cumulative_returns)
                        drawdown = (peak - cumulative_returns)
                        max_drawdown = np.max(drawdown) * 100

            if best_sharpe == float('-inf'):
                return {'error': '无有效结果'}

            return {
                'test_name': test_name,
                'feature_count': len(features),
                'best_train_ratio': best_ratio,
                'sharpe_ratio': best_sharpe,
                'max_drawdown': max_drawdown,
                'features': features
            }

        except Exception as e:
            return {'error': f'测试失败: {e}'}

    def run_ablation_study(self):
        """运行特征消融研究"""
        print("🔬 特征消融测试开始")
        print("=" * 60)

        # 获取数据
        config = TestConfig(
            symbol=self.main_config.symbol,
            timeframe=self.main_config.timeframe,
            lookback_days=self.main_config.lookback_days
        )

        data = self.get_data(config)
        if data.empty:
            print("❌ 数据获取失败")
            return

        print(f"📊 数据: {len(data)}条记录")

        # 计算所有特征
        data = self.calculate_v0_features(data)
        data = self.calculate_additional_features(data)
        data = self.calculate_candlestick_features(data)

        # 测试方案 - 包含K线形态特征
        test_cases = [
            {
                'name': 'v0基础版 (baseline)',
                'features': ['feature_log_return', 'momentum_5m', 'momentum_20m', 'price_position', 'ma_diff']
            },
            {
                'name': 'v0 + RSI',
                'features': ['feature_log_return', 'momentum_5m', 'momentum_20m', 'price_position', 'ma_diff', 'rsi']
            },
            {
                'name': 'v0 + ATR波动率',
                'features': ['feature_log_return', 'momentum_5m', 'momentum_20m', 'price_position', 'ma_diff', 'volatility']
            },
            {
                'name': 'v0 + 动量加速度',
                'features': ['feature_log_return', 'momentum_5m', 'momentum_20m', 'price_position', 'ma_diff', 'momentum_acceleration']
            },
            {
                'name': 'v0 + MACD强度',
                'features': ['feature_log_return', 'momentum_5m', 'momentum_20m', 'price_position', 'ma_diff', 'macd_strength']
            },
            {
                'name': 'v0 + 实体大小特征',
                'features': ['feature_log_return', 'momentum_5m', 'momentum_20m', 'price_position', 'ma_diff', 'body_ratio']
            },
            {
                'name': 'v0 + 影线特征',
                'features': ['feature_log_return', 'momentum_5m', 'momentum_20m', 'price_position', 'ma_diff', 'shadow_ratio']
            },
            {
                'name': 'v0 + Gap跳空特征',
                'features': ['feature_log_return', 'momentum_5m', 'momentum_20m', 'price_position', 'ma_diff', 'gap_pattern']
            },
            {
                'name': 'v0 + Doji十字星特征',
                'features': ['feature_log_return', 'momentum_5m', 'momentum_20m', 'price_position', 'ma_diff', 'doji_pattern']
            },
            {
                'name': 'v0 + 趋势延续特征',
                'features': ['feature_log_return', 'momentum_5m', 'momentum_20m', 'price_position', 'ma_diff', 'trend_continuation']
            }
        ]

        # 运行测试
        results = []
        for test_case in test_cases:
            result = self.run_single_test(data, test_case['features'], test_case['name'])
            if 'error' not in result:
                results.append(result)
                print(f"✅ {test_case['name']}: 夏普比率 {result['sharpe_ratio']:.2f}")
            else:
                print(f"❌ {test_case['name']}: {result['error']}")

        # 分析结果
        self.analyze_results(results)

    def analyze_results(self, results: List[Dict]):
        """分析测试结果"""
        if not results:
            print("❌ 无有效测试结果")
            return

        print("\n" + "=" * 80)
        print("📊 特征消融测试结果分析")
        print("=" * 80)

        # 排序结果
        results.sort(key=lambda x: x['sharpe_ratio'], reverse=True)

        print(f"{'排名':<4} {'测试名称':<20} {'特征数':<6} {'夏普比率':<8} {'最大回撤':<8} {'训练比例':<8}")
        print("-" * 80)

        baseline_sharpe = None
        for i, result in enumerate(results, 1):
            if 'baseline' in result['test_name']:
                baseline_sharpe = result['sharpe_ratio']

            print(f"{i:<4} {result['test_name']:<20} {result['feature_count']:<6} "
                  f"{result['sharpe_ratio']:<8.2f} {result['max_drawdown']:<8.2f}% {result['best_train_ratio']:<8.2f}")

        # 改进分析
        if baseline_sharpe:
            print(f"\n🎯 相对v0基础版改进分析:")
            print(f"基准夏普比率: {baseline_sharpe:.2f}")
            print("-" * 40)

            improvements = []
            for result in results:
                if 'baseline' not in result['test_name']:
                    improvement = ((result['sharpe_ratio'] / baseline_sharpe) - 1) * 100
                    improvements.append({
                        'name': result['test_name'],
                        'improvement': improvement,
                        'sharpe': result['sharpe_ratio']
                    })

            improvements.sort(key=lambda x: x['improvement'], reverse=True)

            for imp in improvements:
                status = "🎉" if imp['improvement'] > 0 else "📉"
                print(f"{status} {imp['name']}: {imp['improvement']:+.1f}% (夏普比率: {imp['sharpe']:.2f})")

        # 推荐方案
        best_result = results[0]
        print(f"\n🏆 最佳方案: {best_result['test_name']}")
        print(f"   夏普比率: {best_result['sharpe_ratio']:.2f}")
        print(f"   特征数量: {best_result['feature_count']}个")
        print(f"   训练比例: {best_result['best_train_ratio']:.2f}")

    def disconnect(self):
        """断开连接"""
        if self.connected:
            mt5.shutdown()

def main():
    """主函数"""
    print("🧪 HMM特征消融测试")
    print("=" * 60)

    if not HMM_AVAILABLE:
        print("❌ 缺少必要依赖")
        return

    # 加载配置
    config_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "config.json")
    main_config = load_config(config_path)

    # 创建测试器
    tester = FeatureAblationTester(main_config)

    try:
        if tester.connect_mt5():
            tester.run_ablation_study()
    finally:
        tester.disconnect()

if __name__ == "__main__":
    main()