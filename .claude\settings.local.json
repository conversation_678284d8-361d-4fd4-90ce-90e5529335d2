{"permissions": {"allow": ["<PERSON><PERSON>(python:*)", "Bash(pip install:*)", "<PERSON><PERSON>(dir)", "Read(/C:\\Users\\<USER>\\Desktop\\mt5-python\\reference/**)", "Read(/C:\\Users\\<USER>\\Desktop\\mt5-python/**)", "Read(/C:\\Users\\<USER>\\Desktop\\mt5-python/**)", "Read(/C:\\Users\\<USER>\\Desktop\\mt5-python/**)", "Read(/C:\\Users\\<USER>\\Desktop\\mt5-python/**)", "Read(/C:\\Users\\<USER>\\Desktop\\mt5-python/**)", "Read(/C:\\Users\\<USER>\\Desktop\\mt5-python/**)", "Read(/C:\\Users\\<USER>\\Desktop\\mt5-python/**)", "Read(/C:\\Users\\<USER>\\Desktop\\mt5-python/**)", "Read(/C:\\Users\\<USER>\\Desktop\\mt5-python/**)", "Read(/C:\\Users\\<USER>\\Desktop\\mt5-python/**)", "Read(/C:\\Users\\<USER>\\Desktop\\mt5-python/**)", "Read(/C:\\Users\\<USER>\\Desktop\\mt5-python/**)", "Read(/C:\\Users\\<USER>\\Desktop\\mt5-python/**)", "Read(/C:\\Users\\<USER>\\Desktop\\mt5-python/**)"], "deny": [], "ask": []}}