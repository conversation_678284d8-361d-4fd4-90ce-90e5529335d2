#!/usr/bin/env python3
"""
特征选择系统主程序
================

使用方法:
1. 特征消融测试: python run_feature_selection.py --mode ablation
2. 智能特征选择: python run_feature_selection.py --mode smart --strategy greedy
3. 自定义测试: python run_feature_selection.py --mode custom --features rsi,volatility
4. 查看帮助: python run_feature_selection.py --help

作者: Augment Agent
日期: 2025-09-27
"""

import argparse
import sys
import os
from pathlib import Path

# 添加上级目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from feature_selection_controller import FeatureSelectionController
from feature_selection_system import SystemConfig

def create_parser():
    """创建命令行参数解析器"""
    parser = argparse.ArgumentParser(
        description="HMM黄金交易特征选择系统",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  # 运行特征消融测试
  python run_feature_selection.py --mode ablation
  
  # 智能贪心搜索
  python run_feature_selection.py --mode smart --strategy greedy --max-features 8
  
  # 穷举搜索 (小规模)
  python run_feature_selection.py --mode smart --strategy exhaustive --max-features 6
  
  # 测试指定特征
  python run_feature_selection.py --mode custom --features rsi,volatility,macd_strength
  
  # 查看可用特征
  python run_feature_selection.py --list-features
        """
    )
    
    parser.add_argument('--mode', choices=['ablation', 'smart', 'custom'], 
                       default='ablation', help='运行模式')
    
    parser.add_argument('--strategy', choices=['greedy', 'exhaustive'], 
                       default='greedy', help='智能搜索策略')
    
    parser.add_argument('--features', type=str, 
                       help='自定义特征列表 (逗号分隔)')
    
    parser.add_argument('--max-features', type=int, default=8,
                       help='最大特征数量')
    
    parser.add_argument('--lookback-days', type=int, default=20,
                       help='历史数据天数')
    
    parser.add_argument('--significance-level', type=float, default=0.05,
                       help='统计显著性水平')
    
    parser.add_argument('--min-improvement', type=float, default=0.02,
                       help='最小改进阈值')
    
    parser.add_argument('--results-dir', type=str, default='results',
                       help='结果保存目录')
    
    parser.add_argument('--no-plot', action='store_true',
                       help='不生成图表')
    
    parser.add_argument('--list-features', action='store_true',
                       help='列出所有可用特征')
    
    parser.add_argument('--verbose', '-v', action='store_true',
                       help='详细输出')
    
    return parser

def main():
    """主函数"""
    parser = create_parser()
    args = parser.parse_args()
    
    # 创建系统配置
    config = SystemConfig(
        lookback_days=args.lookback_days,
        max_features=args.max_features,
        significance_level=args.significance_level,
        min_improvement_threshold=args.min_improvement,
        results_dir=args.results_dir,
        plot_results=not args.no_plot
    )
    
    # 初始化控制器
    try:
        controller = FeatureSelectionController(config)
    except Exception as e:
        print(f"❌ 系统初始化失败: {e}")
        return 1
    
    # 列出特征
    if args.list_features:
        print("\n📋 可用特征列表:")
        print("=" * 60)
        features_df = controller.get_feature_summary()
        
        # 按类别分组显示
        for category in features_df['category'].unique():
            category_features = features_df[features_df['category'] == category]
            print(f"\n🏷️ {category.upper()}:")
            for _, feature in category_features.iterrows():
                print(f"  • {feature['name']:<20} - {feature['description']}")
        
        return 0
    
    print(f"\n🚀 启动特征选择系统")
    print(f"📊 模式: {args.mode}")
    print(f"📅 数据天数: {args.lookback_days}")
    print(f"📁 结果目录: {args.results_dir}")
    print("=" * 60)
    
    # 运行相应模式
    try:
        if args.mode == 'ablation':
            result = run_ablation_mode(controller, args)
        elif args.mode == 'smart':
            result = run_smart_mode(controller, args)
        elif args.mode == 'custom':
            result = run_custom_mode(controller, args)
        else:
            print(f"❌ 未知模式: {args.mode}")
            return 1
        
        # 显示结果摘要
        if result and 'error' not in result:
            print_result_summary(result, controller)
            return 0
        else:
            print(f"❌ 执行失败: {result.get('error', '未知错误')}")
            return 1
            
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断执行")
        return 1
    except Exception as e:
        print(f"❌ 执行出错: {e}")
        if args.verbose:
            import traceback
            traceback.print_exc()
        return 1

def run_ablation_mode(controller: FeatureSelectionController, args) -> dict:
    """运行消融测试模式"""
    print("🔬 运行特征消融测试...")
    
    # 可以指定额外测试的特征
    additional_features = None
    if args.features:
        additional_features = [f.strip() for f in args.features.split(',')]
        print(f"🎯 指定测试特征: {additional_features}")
    
    return controller.run_ablation_study(additional_features)

def run_smart_mode(controller: FeatureSelectionController, args) -> dict:
    """运行智能选择模式"""
    print(f"🧠 运行智能特征选择 ({args.strategy})...")
    
    return controller.run_smart_selection(args.max_features, args.strategy)

def run_custom_mode(controller: FeatureSelectionController, args) -> dict:
    """运行自定义测试模式"""
    if not args.features:
        print("❌ 自定义模式需要指定 --features 参数")
        return {'error': '缺少特征参数'}
    
    features = [f.strip() for f in args.features.split(',')]
    print(f"🎯 测试自定义特征组合: {features}")
    
    # 验证特征是否存在
    available_features = controller.feature_library.get_feature_names()
    invalid_features = [f for f in features if f not in available_features]
    
    if invalid_features:
        print(f"❌ 无效特征: {invalid_features}")
        print(f"💡 可用特征: {available_features}")
        return {'error': f'无效特征: {invalid_features}'}
    
    # 运行单次测试
    if not controller.backtest_engine.connect_mt5():
        return {'error': 'MT5连接失败'}
    
    try:
        data = controller.backtest_engine.get_data()
        if data.empty:
            return {'error': '数据获取失败'}
        
        # 添加基准特征
        baseline_features = controller.feature_library.get_baseline_features()
        test_features = list(set(baseline_features + features))
        
        data_with_features = controller.feature_library.calculate_features(data, test_features)
        
        result = controller.backtest_engine.run_single_backtest(
            data_with_features, test_features, f"custom_{'+'.join(features)}"
        )
        
        if result:
            controller.test_results.append(result)
            return {
                'mode': 'custom',
                'features': test_features,
                'sharpe_ratio': result.sharpe_ratio,
                'total_return': result.total_return,
                'max_drawdown': result.max_drawdown,
                'win_rate': result.win_rate
            }
        else:
            return {'error': '回测执行失败'}
            
    finally:
        controller.backtest_engine.disconnect()

def print_result_summary(result: dict, controller: FeatureSelectionController):
    """打印结果摘要"""
    print("\n" + "=" * 60)
    print("📊 执行结果摘要")
    print("=" * 60)
    
    if result.get('mode') == 'custom':
        print(f"🎯 测试模式: 自定义特征组合")
        print(f"📋 特征列表: {result['features']}")
        print(f"📈 夏普比率: {result['sharpe_ratio']:.2f}")
        print(f"💰 总收益率: {result['total_return']:.2f}%")
        print(f"📉 最大回撤: {result['max_drawdown']:.2f}%")
        print(f"🎯 胜率: {result['win_rate']:.1f}%")
    
    elif 'total_tests' in result:
        print(f"🧪 总测试数: {result['total_tests']}")
        print(f"✅ 成功测试: {result['successful_tests']}")
        
        if result.get('best_feature'):
            print(f"🏆 最佳特征: {result['best_feature']}")
            print(f"📈 改进幅度: {result['best_improvement']:.2f}%")
    
    elif 'strategy' in result:
        print(f"🧠 搜索策略: {result['strategy']}")
        print(f"🎯 最终特征: {result['final_features']}")
        print(f"📈 最终夏普比率: {result['final_sharpe']:.2f}")
        print(f"🧪 总测试数: {result['total_tests']}")
    
    # 显示结果文件位置
    if result.get('results_dir'):
        print(f"\n📁 详细结果已保存至: {result['results_dir']}")
    
    # 显示测试结果表格
    if controller.test_results:
        print(f"\n📋 测试结果排名 (前5名):")
        results_df = controller.get_results_summary()
        print(results_df.head().to_string(index=False, float_format='%.2f'))

if __name__ == "__main__":
    exit(main())
