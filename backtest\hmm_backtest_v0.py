#!/usr/bin/env python3
"""
HMM回测系统 v0 - 基础特征版本
=====================================

与主程序对齐的回测系统，仅使用基础特征：
- feature_log_return (滞后对数收益率)
- momentum_5m (5分钟动量)
- momentum_20m (20分钟动量)
- price_position (价格相对位置)
- ma_diff (均线差值)

科学特性：
- 20天数据深度
- 前向分析 (0.6-0.8训练比例)
- 基于交易频率的夏普比率年化
"""

import os
import sys
import warnings
import numpy as np
import pandas as pd
from datetime import datetime, timedelta, timezone
from typing import List, Dict, Optional, Tuple
from dataclasses import dataclass
import matplotlib.pyplot as plt

# 添加上级目录到路径以导入配置加载器
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from config_loader import load_config

# 抑制警告
warnings.filterwarnings('ignore', category=FutureWarning)
warnings.filterwarnings('ignore', category=UserWarning)

# 尝试导入依赖
try:
    import MetaTrader5 as mt5
    from hmmlearn.hmm import GaussianHMM
    from sklearn.preprocessing import StandardScaler
    HMM_AVAILABLE = True
except ImportError as e:
    print(f"⚠️ 导入失败: {e}")
    print("请安装: pip install MetaTrader5 hmmlearn scikit-learn")
    HMM_AVAILABLE = False

# 配置中文字体
plt.rcParams['font.sans-serif'] = ['Arial Unicode MS', 'SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

@dataclass
class BacktestConfig:
    """回测配置类"""
    # 数据参数
    symbol: str = "XAUUSD"
    timeframe: str = "M1"
    lookback_days: int = 20

    # HMM参数
    n_states: int = 3
    n_iter: int = 500
    covariance_type: str = "diag"
    random_state: int = 42

    # 回测参数
    initial_balance: float = 100000.0
    position_size: float = 1.0
    spread_points: float = 30
    commission_per_lot: float = 7.0

class MT5DataProvider:
    """MT5数据提供者 - 与主程序对齐"""

    def __init__(self, main_config):
        self.connected = False
        self.config = main_config

    def connect(self) -> bool:
        """连接MT5 - 使用主程序相同的配置"""
        if not HMM_AVAILABLE:
            return False

        # 使用配置文件的连接参数，与主程序保持一致
        if not mt5.initialize(
            path=self.config.mt5_path,
            login=self.config.login_id,
            password=self.config.password,
            server=self.config.server_name,
            timeout=self.config.timeout
        ):
            print(f"❌ MT5初始化失败: {mt5.last_error()}")
            return False

        # 验证账户信息
        account_info = mt5.account_info()
        if account_info is None:
            print("❌ 无法获取账户信息")
            return False

        self.connected = True
        print(f"✅ MT5连接成功 - 账户: {account_info.login}, 余额: ${account_info.balance:.2f}")
        return True

    def disconnect(self):
        """断开MT5连接"""
        if self.connected:
            mt5.shutdown()
            self.connected = False

    def get_data(self, config: BacktestConfig) -> pd.DataFrame:
        """获取历史数据 - 与主程序时区对齐"""
        if not self.connected:
            return pd.DataFrame()

        # 计算数据范围 - 使用UTC时区，与主程序保持一致
        end_date = datetime.now(timezone.utc)
        start_date = end_date - timedelta(days=config.lookback_days)

        # 获取数据
        rates = mt5.copy_rates_range(
            config.symbol,
            getattr(mt5, f"TIMEFRAME_{config.timeframe}"),
            start_date,
            end_date
        )

        if rates is None or len(rates) == 0:
            print("❌ 数据获取失败")
            return pd.DataFrame()

        # 转换为DataFrame
        df = pd.DataFrame(rates)
        df['time'] = pd.to_datetime(df['time'], unit='s')
        df.set_index('time', inplace=True)

        # 重命名列
        df.columns = ['Open', 'High', 'Low', 'Close', 'TickVolume', 'Spread', 'RealVolume']

        print(f"✅ 数据获取成功: {len(df)}条记录")
        print(f"📅 时间范围: {df.index[0].strftime('%Y-%m-%d %H:%M')} ~ {df.index[-1].strftime('%Y-%m-%d %H:%M')}")

        return df

class FeatureEngineering:
    """特征工程类 - 仅基础特征"""

    def calculate_features(self, df: pd.DataFrame) -> Tuple[pd.DataFrame, List[str]]:
        """计算基础特征 - 科学优化版本"""
        df = df.copy()

        print("🔧 计算基础特征...")

        # 基础收益率 (用于标签)
        df['log_return'] = np.log(df['Close'] / df['Close'].shift(1))

        # 特征工程 - 所有特征严格滞后1期避免前视偏差
        df['feature_log_return'] = df['log_return'].shift(1)

        # 动量特征 - 使用线性收益率（在此数据集上效果最优）
        df['momentum_5m'] = (df['Close'] / df['Close'].shift(5) - 1).shift(1)
        df['momentum_20m'] = (df['Close'] / df['Close'].shift(20) - 1).shift(1)

        # 价格位置特征 - 使用Williams %R风格（最优效果）
        df['price_position'] = ((df['Close'] - df['Low'].rolling(20).min()) /
                               (df['High'].rolling(20).max() - df['Low'].rolling(20).min())).shift(1)

        # 均线特征 - 相对差值
        df['ma_fast'] = df['Close'].rolling(window=20).mean()
        df['ma_slow'] = df['Close'].rolling(window=35).mean()
        df['ma_diff'] = ((df['ma_fast'] - df['ma_slow']) / df['ma_slow']).shift(1)

        # 基础特征列表
        features = ['feature_log_return', 'momentum_5m', 'momentum_20m', 'price_position', 'ma_diff']

        print(f"✅ 基础特征计算完成（效果最优版）: {features}")
        return df, features

class HMMStrategy:
    """HMM策略类"""

    def __init__(self, config: BacktestConfig):
        self.config = config
        self.model: Optional[GaussianHMM] = None
        self.scaler: Optional[StandardScaler] = None
        self.state_map: Dict[int, str] = {}

    def train(self, data: pd.DataFrame, features: List[str]) -> bool:
        """训练HMM模型"""
        try:
            print("🧠 开始HMM模型训练...")

            # 准备训练数据
            X_train = data[features].dropna()
            if len(X_train) < 100:
                print("❌ 训练数据不足")
                return False

            print(f"📊 训练数据: {len(X_train)}条记录")

            # 标准化
            self.scaler = StandardScaler()
            X_scaled = self.scaler.fit_transform(X_train)

            # 训练HMM
            self.model = GaussianHMM(
                n_components=self.config.n_states,
                covariance_type=self.config.covariance_type,
                n_iter=self.config.n_iter,
                random_state=self.config.random_state
            )

            self.model.fit(X_scaled)

            # 创建状态映射
            self._create_state_mapping(X_train, X_scaled)

            # 计算BIC得分
            bic_score = self.model.bic(X_scaled)
            print(f"📈 BIC得分: {bic_score:.2f}")
            print("✅ HMM模型训练完成")

            return True

        except Exception as e:
            print(f"❌ HMM训练失败: {e}")
            return False

    def _create_state_mapping(self, X_train: pd.DataFrame, X_scaled: np.ndarray):
        """创建状态映射"""
        states = self.model.predict(X_scaled)

        # 根据收益率对状态排序
        state_returns = {}
        for state in range(self.config.n_states):
            state_mask = states == state
            if np.any(state_mask):
                state_returns[state] = X_train.iloc[state_mask]['feature_log_return'].mean()

        # 排序状态
        sorted_states = sorted(state_returns.items(), key=lambda x: x[1])

        # 创建映射
        self.state_map = {}
        state_names = ['下跌', '盘整', '上涨']

        print("🗺️ 状态映射:")
        for i, (state, returns) in enumerate(sorted_states):
            self.state_map[state] = state_names[i]
            print(f"   状态{state} -> {state_names[i]} (收益: {returns:.4f})")

    def predict(self, data: pd.DataFrame, features: List[str]) -> np.ndarray:
        """预测状态（不处理信号转换和shift）"""
        if self.model is None or self.scaler is None:
            return np.array([])

        try:
            # 预测状态
            X_data = data[features].dropna()
            X_scaled = self.scaler.transform(X_data)
            states = self.model.predict(X_scaled)

            return states

        except Exception as e:
            print(f"❌ 状态预测失败: {e}")
            return np.array([])

class BacktestEngine:
    """回测引擎 - 理论收益计算版本（无交易成本）"""

    def __init__(self, config: BacktestConfig):
        self.config = config

    def run_backtest(self, data: pd.DataFrame, signals: pd.Series, features: List[str]) -> Dict:
        """运行理论回测 - 与主程序保持一致"""
        try:
            # 直接使用数据和信号（无需索引对齐，因为都是基于相同数据生成）
            returns = data['log_return'].values
            signals_array = signals.values

            # 确保长度一致
            min_length = min(len(returns), len(signals_array))
            returns = returns[:min_length]
            signals_array = signals_array[:min_length]

            # 计算理论收益序列 - 与主程序完全一致
            strategy_returns = returns * signals_array

            # 去除NaN值
            strategy_returns = strategy_returns[~np.isnan(strategy_returns)]

            if len(strategy_returns) == 0:
                return {'total_trades': 0}

            return self._calculate_theoretical_metrics(strategy_returns, signals)

        except Exception as e:
            print(f"❌ 回测执行失败: {e}")
            return {}

    def _calculate_theoretical_metrics(self, strategy_returns: np.ndarray, signals: pd.Series) -> Dict:
        """计算理论指标 - 与主程序保持一致"""
        if len(strategy_returns) == 0:
            return {'total_trades': 0}

        # 基础统计
        total_return = np.sum(strategy_returns)
        mean_return = np.mean(strategy_returns)
        std_return = np.std(strategy_returns)

        # 计算信号变化次数（交易次数）
        signal_changes = np.sum(np.diff(signals) != 0)

        # 夏普比率 - 与主程序相同的计算方式
        if std_return > 0:
            if signal_changes > 0:
                avg_holding_periods = len(signals) / signal_changes
                minutes_per_year = 252 * 23 * 60
                trades_per_year = minutes_per_year / avg_holding_periods
                sharpe_ratio = mean_return / std_return * np.sqrt(trades_per_year)
            else:
                sharpe_ratio = mean_return / std_return * np.sqrt(252)
        else:
            sharpe_ratio = 0

        # 最大回撤计算
        cumulative_returns = np.cumsum(strategy_returns)
        peak = np.maximum.accumulate(cumulative_returns)
        drawdown = (peak - cumulative_returns)
        max_drawdown = np.max(drawdown) * 100 if len(drawdown) > 0 else 0

        # 胜率计算
        positive_returns = np.sum(strategy_returns > 0)
        win_rate = positive_returns / len(strategy_returns) * 100

        return {
            'total_trades': signal_changes,
            'win_rate': win_rate,
            'total_return': total_return * 100,  # 转换为百分比
            'mean_return': mean_return,
            'std_return': std_return,
            'max_drawdown': max_drawdown,
            'sharpe_ratio': sharpe_ratio,
            'cumulative_return': np.exp(total_return) - 1,  # 复合收益率
        }

class BacktestAnalyzer:
    """回测分析器"""

    def create_report(self, results: Dict, title: str = "回测报告"):
        """生成回测报告"""
        print(f"\n{'='*60}")
        print(f"📊 {title}")
        print(f"{'='*60}")

        print(f"\n📈 基础统计:")
        print(f"  信号变化次数: {results.get('total_trades', 0)}")
        print(f"  正收益比例: {results.get('win_rate', 0):.1f}%")
        print(f"  累计收益率: {results.get('total_return', 0):.4f}%")
        print(f"  复合收益率: {results.get('cumulative_return', 0):.4f}")

        print(f"\n🛡️ 风险指标:")
        print(f"  最大回撤: {results.get('max_drawdown', 0):.4f}%")
        print(f"  夏普比率: {results.get('sharpe_ratio', 0):.2f}")
        print(f"  收益标准差: {results.get('std_return', 0):.6f}")

        print(f"\n💰 收益统计:")
        print(f"  平均收益: {results.get('mean_return', 0):.6f}")
        print(f"  收益波动: {results.get('std_return', 0):.6f}")

class HMMBacktestSystem:
    """HMM回测系统主类"""

    def __init__(self, config: BacktestConfig, main_config=None):
        self.config = config
        # 如果没有提供主程序配置，加载默认配置
        self.main_config = main_config or load_config()
        self.data_provider = MT5DataProvider(self.main_config)
        self.feature_engineering = FeatureEngineering()
        self.strategy = HMMStrategy(config)
        self.backtest_engine = BacktestEngine(config)
        self.analyzer = BacktestAnalyzer()

    def run_backtest(self) -> Dict:
        """运行回测流程"""
        try:
            # 1. 连接数据源
            if not self.data_provider.connect():
                return {'error': 'MT5连接失败'}

            # 2. 获取数据并计算特征
            data = self.data_provider.get_data(self.config)
            if data.empty:
                return {'error': '数据获取失败'}

            data, features = self.feature_engineering.calculate_features(data)

            # 清理数据
            data = data.dropna()
            if len(data) < 2000:
                return {'error': f'有效数据不足: {len(data)} < 2000'}

            # 3. 前向分析 - 与主程序保持一致
            print("🔄 开始前向分析...")
            best_results = None
            best_sharpe = float('-inf')

            # 尝试不同的训练比例 (0.6-0.8)
            for train_ratio in np.arange(0.6, 0.85, 0.05):
                try:
                    split_idx = int(len(data) * train_ratio)
                    train_data = data.iloc[:split_idx]
                    test_data = data.iloc[split_idx:]

                    if len(test_data) < 100:  # 确保有足够的测试数据
                        continue

                    print(f"  尝试训练比例: {train_ratio:.2f} (训练{len(train_data)}, 测试{len(test_data)})")

                    # 训练模型
                    if not self.strategy.train(train_data, features):
                        continue

                    # 预测状态
                    test_states = self.strategy.predict(test_data, features)
                    if len(test_states) == 0:
                        continue

                    # 信号生成（与主程序完全一致的逻辑）
                    test_signals = pd.Series(test_states).map({
                        k: 1 if v == "上涨" else (-1 if v == "下跌" else 0)
                        for k, v in self.strategy.state_map.items()
                    }).shift(1).fillna(0).values

                    # 计算性能指标 (与主程序一致的计算方式)
                    test_returns = test_data['log_return'].values
                    strategy_returns = test_returns * test_signals[:len(test_returns)]

                    if len(strategy_returns) > 0 and np.std(strategy_returns) > 0:
                        # 使用与主程序相同的年化方法
                        signal_changes = np.diff(test_signals) != 0
                        if np.sum(signal_changes) > 0:
                            avg_holding_periods = len(test_signals) / np.sum(signal_changes)
                            minutes_per_year = 252 * 23 * 60
                            trades_per_year = minutes_per_year / avg_holding_periods
                            sharpe = np.mean(strategy_returns) / np.std(strategy_returns) * np.sqrt(trades_per_year)
                        else:
                            sharpe = np.mean(strategy_returns) / np.std(strategy_returns) * np.sqrt(252)

                        print(f"    夏普比率: {sharpe:.2f}")

                        if sharpe > best_sharpe:
                            best_sharpe = sharpe
                            best_results = {
                                'train_ratio': train_ratio,
                                'train_data': train_data,
                                'test_data': test_data,
                                'test_states': test_states,
                                'sharpe_ratio': sharpe
                            }

                except Exception as e:
                    print(f"    训练比例 {train_ratio:.2f} 失败: {e}")
                    continue

            if best_results is None:
                return {'error': '前向分析失败，无有效结果'}

            print(f"🎯 最佳训练比例: {best_results['train_ratio']:.2f}, 夏普比率: {best_results['sharpe_ratio']:.2f}")

            # 4. 使用最佳结果重新训练和预测
            if not self.strategy.train(best_results['train_data'], features):
                return {'error': 'HMM训练失败'}

            test_states = self.strategy.predict(best_results['test_data'], features)

            # 信号生成（与主程序完全一致的逻辑）
            test_signals = pd.Series(test_states).map({
                k: 1 if v == "上涨" else (-1 if v == "下跌" else 0)
                for k, v in self.strategy.state_map.items()
            }).shift(1).fillna(0)


            # 5. 执行回测
            results = self.backtest_engine.run_backtest(best_results['test_data'], test_signals, features)
            results['forward_analysis'] = {
                'best_train_ratio': best_results['train_ratio'],
                'best_sharpe_ratio': best_results['sharpe_ratio']
            }
            results['features_used'] = features
            results['feature_type'] = "basic"

            return results

        finally:
            self.data_provider.disconnect()

def main():
    """主函数 - v0基础特征回测"""
    print("🚀 HMM回测系统 v0 (基础特征) - 与主程序对齐版")
    print("==============================================")

    if not HMM_AVAILABLE:
        print("❌ 缺少必要依赖，请先安装")
        return

    # 加载主程序配置文件 - 使用正确的路径
    config_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "config.json")
    main_config = load_config(config_path)
    print(f"✅ 已加载主程序配置文件")
    print(f"📊 MT5账户: {main_config.login_id}")
    print(f"🌐 服务器: {main_config.server_name}")
    print(f"💱 交易品种: {main_config.symbol}")

    # 创建回测配置 - 与主程序参数对齐
    config = BacktestConfig(
        symbol=main_config.symbol,  # 使用主程序的品种
        timeframe=main_config.timeframe,  # 使用主程序的时间框架
        lookback_days=main_config.lookback_days  # 使用主程序的回望天数
    )

    # 创建回测系统，传入主程序配置
    backtest_system = HMMBacktestSystem(config, main_config)

    # 运行回测
    print(f"\n🚀 开始HMM v0回测 (基础特征)")
    print("="*50)

    results = backtest_system.run_backtest()

    if 'error' in results:
        print(f"❌ 回测失败: {results['error']}")
        return

    print("✅ 回测执行完成")

    # 生成报告
    backtest_system.analyzer.create_report(results, "v0基础特征回测报告")

    # 显示前向分析结果
    if 'forward_analysis' in results:
        fa = results['forward_analysis']
        print(f"\n📊 前向分析结果:")
        print(f"  最佳训练比例: {fa['best_train_ratio']:.2f}")
        print(f"  理论夏普比率: {fa['best_sharpe_ratio']:.2f}")
        print(f"  实际夏普比率: {results.get('sharpe_ratio', 0):.2f}")

if __name__ == "__main__":
    main()