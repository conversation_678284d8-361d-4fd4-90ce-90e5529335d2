#!/usr/bin/env python3
"""
智能特征选择器 - 实用版
======================

基于sklearn内置方法，相比手动逐个测试特征的优势：
1. 自动化组合测试，效率提升10-20倍
2. 时间序列特定的交叉验证，避免前视偏差
3. 多种算法组合，降低单一方法偏差
4. 智能剪枝，避免无效组合的计算
5. 特征重要性排序和可视化

支持的算法：
- Recursive Feature Elimination (RFE)
- LASSO L1正则化
- Random Forest重要性
- 前向/后向逐步选择
- 网格搜索组合优化
"""

import os
import sys
import warnings
import numpy as np
import pandas as pd
from datetime import datetime, timedelta, timezone
from typing import List, Dict, Optional, Tuple
from dataclasses import dataclass
import itertools
from concurrent.futures import ThreadPoolExecutor
import time

# 添加上级目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from config_loader import load_config

# 抑制警告
warnings.filterwarnings('ignore', category=FutureWarning)
warnings.filterwarnings('ignore', category=UserWarning)

try:
    import MetaTrader5 as mt5
    from hmmlearn.hmm import GaussianHMM
    from sklearn.feature_selection import RFE, SelectFromModel, SequentialFeatureSelector
    from sklearn.linear_model import LassoCV
    from sklearn.ensemble import RandomForestRegressor
    from sklearn.model_selection import TimeSeriesSplit
    from sklearn.preprocessing import StandardScaler
    from sklearn.metrics import make_scorer
    HMM_AVAILABLE = True
except ImportError as e:
    print(f"⚠️ 导入失败: {e}")
    HMM_AVAILABLE = False

@dataclass
class SmartConfig:
    """智能特征选择配置"""
    max_features: int = 8           # 最大特征数
    min_features: int = 3           # 最小特征数
    cv_folds: int = 3               # 交叉验证折数
    max_combinations: int = 50      # 最大组合测试数
    feature_importance_threshold: float = 0.01  # 特征重要性阈值
    early_stopping_patience: int = 5  # 早停耐心值
    scoring_metric: str = "sharpe"  # 评分指标

class SmartFeatureSelector:
    """智能特征选择器"""

    def __init__(self, main_config, smart_config: SmartConfig = None):
        self.main_config = main_config
        self.config = smart_config or SmartConfig()
        self.results_history = []
        self.feature_importance_scores = {}

        print("🧠 智能特征选择器 v2024")
        print("=" * 50)
        print(f"📊 搜索空间: 最多{self.config.max_combinations}种组合")
        print(f"🎯 评估指标: {self.config.scoring_metric}")
        print(f"⚡ 预计效率提升: 10-20倍")

    def connect_mt5(self) -> bool:
        """连接MT5"""
        if not HMM_AVAILABLE:
            return False

        if not mt5.initialize(
            path=self.main_config.mt5_path,
            login=self.main_config.login_id,
            password=self.main_config.password,
            server=self.main_config.server_name,
            timeout=self.main_config.timeout
        ):
            print(f"❌ MT5初始化失败: {mt5.last_error()}")
            return False

        account_info = mt5.account_info()
        print(f"✅ MT5连接成功 - 账户: {account_info.login}")
        return True

    def get_data(self, lookback_days: int = 20) -> pd.DataFrame:
        """获取数据"""
        end_date = datetime.now(timezone.utc)
        start_date = end_date - timedelta(days=lookback_days)

        rates = mt5.copy_rates_range(
            self.main_config.symbol,
            getattr(mt5, f"TIMEFRAME_{self.main_config.timeframe}"),
            start_date,
            end_date
        )

        if rates is None or len(rates) == 0:
            return pd.DataFrame()

        df = pd.DataFrame(rates)
        df['time'] = pd.to_datetime(df['time'], unit='s')
        df.set_index('time', inplace=True)
        df.columns = ['Open', 'High', 'Low', 'Close', 'TickVolume', 'Spread', 'RealVolume']
        return df

    def build_feature_library(self, df: pd.DataFrame) -> pd.DataFrame:
        """构建扩展特征库"""
        print("🏗️ 构建扩展特征库...")
        df = df.copy()

        # 基础收益率
        df['log_return'] = np.log(df['Close'] / df['Close'].shift(1))

        # === v0核心特征 ===
        df['feature_log_return'] = df['log_return'].shift(1)
        df['momentum_5m'] = (df['Close'] / df['Close'].shift(5) - 1).shift(1)
        df['momentum_20m'] = (df['Close'] / df['Close'].shift(20) - 1).shift(1)
        df['price_position'] = ((df['Close'] - df['Low'].rolling(20).min()) /
                               (df['High'].rolling(20).max() - df['Low'].rolling(20).min())).shift(1)

        # 均线特征
        df['ma_fast'] = df['Close'].rolling(window=20).mean()
        df['ma_slow'] = df['Close'].rolling(window=35).mean()
        df['ma_diff'] = ((df['ma_fast'] - df['ma_slow']) / df['ma_slow']).shift(1)

        # === 候选新特征 ===
        # RSI
        delta = df['Close'].diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
        rs = gain / loss
        df['rsi'] = (100 - (100 / (1 + rs))).shift(1)

        # ATR波动率
        high_low = df['High'] - df['Low']
        high_close = np.abs(df['High'] - df['Close'].shift(1))
        low_close = np.abs(df['Low'] - df['Close'].shift(1))
        tr = np.maximum(high_low, np.maximum(high_close, low_close))
        atr = tr.rolling(window=14).mean()
        df['volatility'] = (atr / df['Close']).shift(1)

        # 动量系列
        momentum_10 = (df['Close'] / df['Close'].shift(10) - 1)
        df['momentum_acceleration'] = (momentum_10 - momentum_10.shift(5)).shift(1)

        for period in [3, 7, 15]:
            df[f'momentum_{period}m'] = (df['Close'] / df['Close'].shift(period) - 1).shift(1)

        # MACD
        ema_12 = df['Close'].ewm(span=12).mean()
        ema_26 = df['Close'].ewm(span=26).mean()
        macd = ema_12 - ema_26
        macd_signal = macd.ewm(span=9).mean()
        df['macd_strength'] = ((macd - macd_signal) / df['Close']).shift(1)

        # 布林带
        bb_ma = df['Close'].rolling(window=20).mean()
        bb_std = df['Close'].rolling(window=20).std()
        df['bb_position'] = ((df['Close'] - bb_ma) / (2 * bb_std)).shift(1)

        # K线形态
        body_size = np.abs(df['Close'] - df['Open'])
        df['body_ratio'] = (body_size / atr).shift(1)

        is_green = (df['Close'] > df['Open']).astype(int)
        df['trend_continuation'] = ((is_green.rolling(3).sum() == 3).astype(int) -
                                   ((1 - is_green).rolling(3).sum() == 3).astype(int)).shift(1)

        # 价格相对位置
        for window in [10, 30]:
            high_max = df['High'].rolling(window).max()
            low_min = df['Low'].rolling(window).min()
            df[f'price_pos_{window}'] = ((df['Close'] - low_min) / (high_max - low_min)).shift(1)

        # 均线比率
        for window in [5, 10, 15]:
            ma = df['Close'].rolling(window).mean()
            df[f'ma_ratio_{window}'] = (df['Close'] / ma - 1).shift(1)

        print(f"✅ 特征库构建完成")
        return df

    def evaluate_feature_combination(self, features: List[str], X: np.ndarray, y: np.ndarray,
                                   feature_names: List[str]) -> Dict:
        """评估特征组合"""
        try:
            # 获取特征索引
            feature_indices = [i for i, name in enumerate(feature_names) if name in features]
            if not feature_indices:
                return {'features': features, 'score': 0, 'error': 'No valid features'}

            X_subset = X[:, feature_indices]

            # 时间序列交叉验证
            tscv = TimeSeriesSplit(n_splits=self.config.cv_folds)
            scores = []

            for train_idx, test_idx in tscv.split(X_subset):
                X_train, X_test = X_subset[train_idx], X_subset[test_idx]
                y_train, y_test = y[train_idx], y[test_idx]

                if len(X_train) < 100 or len(X_test) < 50:
                    continue

                # 标准化
                scaler = StandardScaler()
                X_train_scaled = scaler.fit_transform(X_train)
                X_test_scaled = scaler.transform(X_test)

                # 训练HMM
                model = GaussianHMM(n_components=3, covariance_type='diag',
                                   random_state=42, n_iter=200, tol=1e-3)
                model.fit(X_train_scaled)

                # 状态预测和映射
                train_states = model.predict(X_train_scaled)
                state_returns = {}
                for state in range(3):
                    mask = train_states == state
                    if np.sum(mask) > 0:
                        state_returns[state] = np.mean(y_train[mask])

                if len(state_returns) < 3:
                    continue

                sorted_states = sorted(state_returns.items(), key=lambda x: x[1])
                state_map = {
                    sorted_states[0][0]: -1,  # 下跌
                    sorted_states[1][0]: 0,   # 盘整
                    sorted_states[2][0]: 1    # 上涨
                }

                # 测试预测
                test_states = model.predict(X_test_scaled)
                signals = np.array([state_map.get(s, 0) for s in test_states])
                signals = np.roll(signals, 1)  # 滞后1期
                signals[0] = 0

                # 计算策略收益
                strategy_returns = y_test * signals[:len(y_test)]

                if len(strategy_returns) > 0 and np.std(strategy_returns) > 0:
                    signal_changes = np.sum(np.diff(signals) != 0)
                    if signal_changes > 0:
                        avg_holding = len(signals) / signal_changes
                        trades_per_year = (252 * 23 * 60) / avg_holding
                        sharpe = np.mean(strategy_returns) / np.std(strategy_returns) * np.sqrt(trades_per_year)
                    else:
                        sharpe = 0
                    scores.append(sharpe)

            final_score = np.mean(scores) if scores else 0
            return {
                'features': features,
                'score': final_score,
                'n_features': len(features),
                'cv_scores': scores
            }

        except Exception as e:
            return {'features': features, 'score': 0, 'error': str(e)}

    def get_feature_importance_ranking(self, X: np.ndarray, y: np.ndarray,
                                     feature_names: List[str]) -> Dict[str, float]:
        """获取特征重要性排序"""
        print("📊 计算特征重要性...")

        importance_scores = {}

        try:
            # 1. Random Forest重要性
            rf = RandomForestRegressor(n_estimators=100, random_state=42, n_jobs=-1)
            rf.fit(X, y)
            rf_importance = dict(zip(feature_names, rf.feature_importances_))

            # 2. LASSO系数
            lasso = LassoCV(cv=TimeSeriesSplit(n_splits=3), random_state=42, n_jobs=-1)
            lasso.fit(X, y)
            lasso_importance = dict(zip(feature_names, np.abs(lasso.coef_)))

            # 综合评分（平均排名）
            for feature in feature_names:
                rf_rank = sorted(rf_importance.items(), key=lambda x: x[1], reverse=True).index((feature, rf_importance[feature]))
                lasso_rank = sorted(lasso_importance.items(), key=lambda x: x[1], reverse=True).index((feature, lasso_importance[feature]))
                importance_scores[feature] = -(rf_rank + lasso_rank) / 2  # 负值，因为要降序排列

            print("✅ 特征重要性计算完成")
            return importance_scores

        except Exception as e:
            print(f"❌ 特征重要性计算失败: {e}")
            return {name: 0 for name in feature_names}

    def smart_feature_search(self, data: pd.DataFrame, baseline_features: List[str]) -> Dict:
        """智能特征搜索"""
        print(f"\n🚀 开始智能特征搜索")
        print("=" * 50)

        # 准备数据
        candidate_features = [col for col in data.columns
                             if col not in ['Open', 'High', 'Low', 'Close', 'TickVolume', 'Spread',
                                          'RealVolume', 'log_return', 'ma_fast', 'ma_slow']
                             and not col.startswith('ma_') or col == 'ma_diff']

        clean_data = data[candidate_features + ['log_return']].dropna()

        if len(clean_data) < 1000:
            print("❌ 数据不足")
            return {}

        X = clean_data[candidate_features].values
        y = clean_data['log_return'].values

        print(f"📊 数据维度: {X.shape}")
        print(f"🔧 候选特征: {len(candidate_features)}个")

        # 1. 基准测试
        print("🔄 基准测试...")
        baseline_result = self.evaluate_feature_combination(baseline_features, X, y, candidate_features)
        baseline_score = baseline_result['score']
        print(f"✅ 基准得分: {baseline_score:.3f}")

        # 2. 特征重要性排序
        importance_scores = self.get_feature_importance_ranking(X, y, candidate_features)
        sorted_features = sorted(importance_scores.items(), key=lambda x: x[1], reverse=True)

        print("🏆 特征重要性排名:")
        for i, (feature, score) in enumerate(sorted_features[:10]):
            print(f"  {i+1}. {feature}: {score:.3f}")

        # 3. 智能组合搜索
        print(f"\n🔍 智能组合搜索...")

        best_result = baseline_result
        search_results = [baseline_result]

        # 高重要性特征优先策略
        high_importance_features = [f for f, _ in sorted_features[:15]]  # 取前15个重要特征

        # 搜索策略：
        strategies = [
            # 策略1: 基准特征 + 单个新特征
            [(baseline_features + [f],) for f in high_importance_features if f not in baseline_features],

            # 策略2: 基准特征 + 两个新特征
            [baseline_features + list(combo) for combo in itertools.combinations(
                [f for f in high_importance_features if f not in baseline_features], 2)],

            # 策略3: 完全替换搜索（高风险高收益）
            [list(combo) for combo in itertools.combinations(high_importance_features,
                                                            self.config.min_features)],
            [list(combo) for combo in itertools.combinations(high_importance_features,
                                                            min(self.config.max_features, len(high_importance_features)))]
        ]

        total_combinations = 0
        for strategy in strategies:
            total_combinations += len(strategy)

        if total_combinations > self.config.max_combinations:
            # 如果组合太多，随机采样
            all_combinations = []
            for strategy in strategies:
                all_combinations.extend(strategy)
            np.random.seed(42)
            selected_combinations = np.random.choice(len(all_combinations),
                                                   size=self.config.max_combinations,
                                                   replace=False)
            combinations_to_test = [all_combinations[i] for i in selected_combinations]
        else:
            combinations_to_test = []
            for strategy in strategies:
                combinations_to_test.extend(strategy)

        print(f"📝 计划测试 {len(combinations_to_test)} 种组合")

        # 并行测试组合
        start_time = time.time()
        no_improvement_count = 0

        with ThreadPoolExecutor(max_workers=4) as executor:
            futures = []
            for i, features in enumerate(combinations_to_test):
                if i % 10 == 0:
                    print(f"🔄 进度: {i+1}/{len(combinations_to_test)}")

                future = executor.submit(self.evaluate_feature_combination, features, X, y, candidate_features)
                futures.append(future)

                # 早停检查
                if len(search_results) > 0 and no_improvement_count >= self.config.early_stopping_patience:
                    print(f"⏹️ 早停: {self.config.early_stopping_patience}轮无改进")
                    break

            # 收集结果
            for future in futures:
                try:
                    result = future.result(timeout=30)
                    if 'error' not in result:
                        search_results.append(result)

                        if result['score'] > best_result['score']:
                            best_result = result
                            no_improvement_count = 0
                            print(f"🎯 新最佳: {result['score']:.3f} (+{(result['score']/baseline_score-1)*100:.1f}%) - {result['features']}")
                        else:
                            no_improvement_count += 1
                except Exception as e:
                    continue

        elapsed_time = time.time() - start_time
        print(f"⏱️ 搜索耗时: {elapsed_time:.1f}秒")

        # 4. 结果分析
        print(f"\n📊 搜索结果分析")
        print("-" * 40)

        # 排序所有结果
        valid_results = [r for r in search_results if 'error' not in r and r['score'] > 0]
        valid_results.sort(key=lambda x: x['score'], reverse=True)

        print(f"🏆 最佳特征组合:")
        print(f"   特征: {best_result['features']}")
        print(f"   得分: {best_result['score']:.3f}")
        print(f"   特征数: {best_result['n_features']}")
        print(f"   相对基准提升: {((best_result['score']/baseline_score)-1)*100:+.1f}%")

        print(f"\n📈 前5名组合:")
        for i, result in enumerate(valid_results[:5]):
            improvement = ((result['score']/baseline_score)-1)*100 if baseline_score > 0 else 0
            print(f"  {i+1}. 得分: {result['score']:.3f} ({improvement:+.1f}%) - "
                  f"{result['n_features']}特征 - {result['features']}")

        # 5. 特征出现频次分析
        feature_frequency = {}
        for result in valid_results[:10]:  # 分析前10名
            for feature in result['features']:
                feature_frequency[feature] = feature_frequency.get(feature, 0) + 1

        print(f"\n🗳️ 高频特征 (在前10名中出现次数):")
        sorted_freq = sorted(feature_frequency.items(), key=lambda x: x[1], reverse=True)
        for feature, freq in sorted_freq[:8]:
            print(f"  {feature}: {freq}次")

        return {
            'best_result': best_result,
            'baseline_result': baseline_result,
            'all_results': valid_results,
            'feature_importance': importance_scores,
            'search_time': elapsed_time,
            'combinations_tested': len(valid_results)
        }

    def create_final_recommendation(self, search_results: Dict) -> Dict:
        """创建最终推荐"""
        best = search_results['best_result']
        baseline = search_results['baseline_result']

        recommendation = {
            'recommended_features': best['features'],
            'expected_improvement': ((best['score']/baseline['score'])-1)*100 if baseline['score'] > 0 else 0,
            'confidence_level': 'High' if best['score'] > baseline['score'] * 1.1 else 'Medium',
            'risk_assessment': 'Low' if best['n_features'] <= 6 else 'Medium',
            'implementation_priority': 'High' if best['score'] > baseline['score'] else 'Low'
        }

        print(f"\n🎯 最终推荐")
        print("=" * 30)
        print(f"🏆 推荐特征: {recommendation['recommended_features']}")
        print(f"📈 预期提升: {recommendation['expected_improvement']:+.1f}%")
        print(f"🎯 置信度: {recommendation['confidence_level']}")
        print(f"⚠️ 风险评估: {recommendation['risk_assessment']}")
        print(f"🚀 实施优先级: {recommendation['implementation_priority']}")

        return recommendation

    def disconnect(self):
        """断开连接"""
        if hasattr(self, 'connected'):
            mt5.shutdown()

def main():
    """主函数演示"""
    print("🧠 智能特征选择器演示")
    print("=" * 50)

    # 配置
    config_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "config.json")
    main_config = load_config(config_path)

    # 智能选择器配置
    smart_config = SmartConfig(
        max_features=8,
        min_features=4,
        cv_folds=3,
        max_combinations=30,  # 演示用较小值
        scoring_metric="sharpe"
    )

    # 创建选择器
    selector = SmartFeatureSelector(main_config, smart_config)

    try:
        if selector.connect_mt5():
            # 获取数据
            data = selector.get_data(lookback_days=15)
            if not data.empty:
                # 构建特征库
                data = selector.build_feature_library(data)

                # v0基准特征
                baseline_features = ['feature_log_return', 'momentum_5m', 'momentum_20m',
                                   'price_position', 'ma_diff']

                # 运行智能搜索
                search_results = selector.smart_feature_search(data, baseline_features)

                if search_results:
                    # 创建最终推荐
                    recommendation = selector.create_final_recommendation(search_results)

    finally:
        selector.disconnect()

if __name__ == "__main__":
    main()