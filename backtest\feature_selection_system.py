#!/usr/bin/env python3
"""
系统化特征选择框架 - 统一的特征测试和评估系统
==============================================

核心设计理念：
1. 统一的特征库管理 - 避免重复代码，支持动态扩展
2. 标准化的回测流程 - 确保公平比较
3. 科学的统计分析 - 验证特征的真实效果
4. 自动化的测试流程 - 提高效率和一致性
5. 可视化的结果展示 - 便于理解和决策

作者: Augment Agent
日期: 2025-09-27
"""

import os
import sys
import warnings
import numpy as np
import pandas as pd
from datetime import datetime, timedelta, timezone
from typing import List, Dict, Optional, Tuple, Callable, Any
from dataclasses import dataclass, field
from abc import ABC, abstractmethod
import json
import pickle
from pathlib import Path
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import stats
import itertools
from concurrent.futures import ThreadPoolExecutor, as_completed
import time

# 添加上级目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from config_loader import load_config

# 抑制警告
warnings.filterwarnings('ignore', category=FutureWarning)
warnings.filterwarnings('ignore', category=UserWarning)

try:
    import MetaTrader5 as mt5
    from hmmlearn.hmm import GaussianHMM
    from sklearn.preprocessing import StandardScaler
    from sklearn.model_selection import TimeSeriesSplit
    from sklearn.metrics import make_scorer
    HMM_AVAILABLE = True
except ImportError as e:
    print(f"⚠️ 导入失败: {e}")
    HMM_AVAILABLE = False

# 配置中文字体
plt.rcParams['font.sans-serif'] = ['Arial Unicode MS', 'SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

@dataclass
class SystemConfig:
    """系统配置类"""
    # 数据参数
    symbol: str = "XAUUSD"
    timeframe: str = "M1"
    lookback_days: int = 20
    
    # HMM参数
    n_states: int = 3
    n_iter: int = 500
    covariance_type: str = "diag"
    random_state: int = 42
    
    # 回测参数
    train_ratios: List[float] = field(default_factory=lambda: [0.6, 0.65, 0.7, 0.75, 0.8])
    min_test_samples: int = 100
    min_train_samples: int = 2000
    
    # 特征选择参数
    max_features: int = 10
    min_features: int = 3
    significance_level: float = 0.05
    min_improvement_threshold: float = 0.02  # 最小改进阈值 (2%)
    
    # 并行处理
    n_jobs: int = -1
    max_workers: int = 4
    
    # 输出控制
    save_results: bool = True
    results_dir: str = "results"
    plot_results: bool = True

class FeatureDefinition:
    """特征定义类"""
    
    def __init__(self, name: str, calculation_func: Callable, 
                 description: str = "", category: str = "custom",
                 dependencies: List[str] = None):
        self.name = name
        self.calculation_func = calculation_func
        self.description = description
        self.category = category
        self.dependencies = dependencies or []
        self.created_at = datetime.now()
    
    def calculate(self, df: pd.DataFrame) -> pd.Series:
        """计算特征值"""
        try:
            return self.calculation_func(df)
        except Exception as e:
            print(f"❌ 特征 {self.name} 计算失败: {e}")
            return pd.Series(index=df.index, dtype=float)
    
    def __repr__(self):
        return f"FeatureDefinition(name='{self.name}', category='{self.category}')"

class FeatureLibrary:
    """特征库管理类"""
    
    def __init__(self):
        self.features: Dict[str, FeatureDefinition] = {}
        self.categories: Dict[str, List[str]] = {}
        self._initialize_core_features()
    
    def _initialize_core_features(self):
        """初始化核心特征"""
        
        # === v0核心特征 ===
        self.register_feature(
            "feature_log_return",
            lambda df: np.log(df['Close'] / df['Close'].shift(1)).shift(1),
            "滞后对数收益率",
            "basic"
        )
        
        self.register_feature(
            "momentum_5m",
            lambda df: (df['Close'] / df['Close'].shift(5) - 1).shift(1),
            "5分钟动量",
            "momentum"
        )
        
        self.register_feature(
            "momentum_20m", 
            lambda df: (df['Close'] / df['Close'].shift(20) - 1).shift(1),
            "20分钟动量",
            "momentum"
        )
        
        self.register_feature(
            "price_position",
            lambda df: ((df['Close'] - df['Low'].rolling(20).min()) /
                       (df['High'].rolling(20).max() - df['Low'].rolling(20).min())).shift(1),
            "价格相对位置 (Williams %R风格)",
            "position"
        )
        
        self.register_feature(
            "ma_diff",
            lambda df: ((df['Close'].rolling(20).mean() - df['Close'].rolling(35).mean()) /
                       df['Close'].rolling(35).mean()).shift(1),
            "均线差值",
            "moving_average"
        )
        
        # === 技术指标特征 ===
        self.register_feature(
            "rsi",
            self._calculate_rsi,
            "相对强弱指标",
            "oscillator"
        )
        
        self.register_feature(
            "volatility",
            self._calculate_atr_volatility,
            "ATR波动率",
            "volatility"
        )
        
        self.register_feature(
            "momentum_acceleration",
            lambda df: ((df['Close'] / df['Close'].shift(10) - 1) - 
                       (df['Close'] / df['Close'].shift(10) - 1).shift(5)).shift(1),
            "动量加速度",
            "momentum"
        )
        
        self.register_feature(
            "macd_strength",
            self._calculate_macd_strength,
            "MACD强度",
            "oscillator"
        )
        
        # === K线形态特征 ===
        self.register_feature(
            "body_ratio",
            self._calculate_body_ratio,
            "实体大小比率",
            "candlestick"
        )
        
        self.register_feature(
            "shadow_ratio",
            self._calculate_shadow_ratio,
            "影线比率",
            "candlestick"
        )
        
        self.register_feature(
            "gap_pattern",
            self._calculate_gap_pattern,
            "跳空形态",
            "candlestick"
        )
        
        self.register_feature(
            "doji_pattern",
            self._calculate_doji_pattern,
            "十字星形态",
            "candlestick"
        )
        
        self.register_feature(
            "trend_continuation",
            self._calculate_trend_continuation,
            "趋势延续形态",
            "candlestick"
        )
    
    def register_feature(self, name: str, calculation_func: Callable,
                        description: str = "", category: str = "custom",
                        dependencies: List[str] = None):
        """注册新特征"""
        feature = FeatureDefinition(name, calculation_func, description, category, dependencies)
        self.features[name] = feature
        
        if category not in self.categories:
            self.categories[category] = []
        self.categories[category].append(name)
        
        print(f"✅ 注册特征: {name} ({category})")
    
    def get_feature_names(self, category: str = None) -> List[str]:
        """获取特征名称列表"""
        if category is None:
            return list(self.features.keys())
        return self.categories.get(category, [])
    
    def get_baseline_features(self) -> List[str]:
        """获取基准特征 (v0核心特征)"""
        return ['feature_log_return', 'momentum_5m', 'momentum_20m', 'price_position', 'ma_diff']
    
    def calculate_features(self, df: pd.DataFrame, feature_names: List[str] = None) -> pd.DataFrame:
        """计算指定特征"""
        if feature_names is None:
            feature_names = self.get_feature_names()
        
        result_df = df.copy()
        
        # 添加基础收益率
        if 'log_return' not in result_df.columns:
            result_df['log_return'] = np.log(result_df['Close'] / result_df['Close'].shift(1))
        
        # 计算特征
        for name in feature_names:
            if name in self.features:
                try:
                    result_df[name] = self.features[name].calculate(result_df)
                except Exception as e:
                    print(f"❌ 计算特征 {name} 失败: {e}")
        
        return result_df
    
    # === 特征计算函数 ===
    def _calculate_rsi(self, df: pd.DataFrame, period: int = 14) -> pd.Series:
        """计算RSI"""
        delta = df['Close'].diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
        rs = gain / loss
        return (100 - (100 / (1 + rs))).shift(1)
    
    def _calculate_atr_volatility(self, df: pd.DataFrame, period: int = 14) -> pd.Series:
        """计算ATR波动率"""
        high_low = df['High'] - df['Low']
        high_close = np.abs(df['High'] - df['Close'].shift(1))
        low_close = np.abs(df['Low'] - df['Close'].shift(1))
        tr = np.maximum(high_low, np.maximum(high_close, low_close))
        atr = tr.rolling(window=period).mean()
        return (atr / df['Close']).shift(1)
    
    def _calculate_macd_strength(self, df: pd.DataFrame) -> pd.Series:
        """计算MACD强度"""
        ema_12 = df['Close'].ewm(span=12).mean()
        ema_26 = df['Close'].ewm(span=26).mean()
        macd = ema_12 - ema_26
        macd_signal = macd.ewm(span=9).mean()
        return ((macd - macd_signal) / df['Close']).shift(1)
    
    def _calculate_body_ratio(self, df: pd.DataFrame) -> pd.Series:
        """计算实体大小比率"""
        high_low = df['High'] - df['Low']
        high_close = np.abs(df['High'] - df['Close'].shift(1))
        low_close = np.abs(df['Low'] - df['Close'].shift(1))
        tr = np.maximum(high_low, np.maximum(high_close, low_close))
        atr_20 = tr.rolling(window=20).mean()
        body_size = np.abs(df['Close'] - df['Open'])
        return (body_size / atr_20).shift(1)
    
    def _calculate_shadow_ratio(self, df: pd.DataFrame) -> pd.Series:
        """计算影线比率"""
        high_low = df['High'] - df['Low']
        high_close = np.abs(df['High'] - df['Close'].shift(1))
        low_close = np.abs(df['Low'] - df['Close'].shift(1))
        tr = np.maximum(high_low, np.maximum(high_close, low_close))
        atr_20 = tr.rolling(window=20).mean()
        
        upper_shadow = df['High'] - np.maximum(df['Open'], df['Close'])
        lower_shadow = np.minimum(df['Open'], df['Close']) - df['Low']
        return ((upper_shadow + lower_shadow) / atr_20).shift(1)
    
    def _calculate_gap_pattern(self, df: pd.DataFrame) -> pd.Series:
        """计算跳空形态"""
        prev_close = df['Close'].shift(1)
        gap_up = (df['Low'] > prev_close).astype(int)
        gap_down = (df['High'] < prev_close).astype(int)
        return (gap_up - gap_down).shift(1)
    
    def _calculate_doji_pattern(self, df: pd.DataFrame) -> pd.Series:
        """计算十字星形态"""
        high_low = df['High'] - df['Low']
        high_close = np.abs(df['High'] - df['Close'].shift(1))
        low_close = np.abs(df['Low'] - df['Close'].shift(1))
        tr = np.maximum(high_low, np.maximum(high_close, low_close))
        atr_20 = tr.rolling(window=20).mean()
        
        body_size = np.abs(df['Close'] - df['Open'])
        upper_shadow = df['High'] - np.maximum(df['Open'], df['Close'])
        lower_shadow = np.minimum(df['Open'], df['Close']) - df['Low']
        
        is_doji = (body_size / atr_20 < 0.1) & ((upper_shadow + lower_shadow) / atr_20 > 0.3)
        return is_doji.astype(float).shift(1)
    
    def _calculate_trend_continuation(self, df: pd.DataFrame) -> pd.Series:
        """计算趋势延续形态"""
        is_green = (df['Close'] > df['Open']).astype(int)
        consecutive_green = is_green.rolling(3).sum()
        consecutive_red = (1 - is_green).rolling(3).sum()
        return ((consecutive_green == 3).astype(int) - (consecutive_red == 3).astype(int)).shift(1)
    
    def list_features(self) -> pd.DataFrame:
        """列出所有特征"""
        data = []
        for name, feature in self.features.items():
            data.append({
                'name': name,
                'category': feature.category,
                'description': feature.description,
                'created_at': feature.created_at.strftime('%Y-%m-%d %H:%M')
            })
        return pd.DataFrame(data)

@dataclass
class BacktestResult:
    """回测结果类"""
    feature_set: List[str]
    test_name: str
    sharpe_ratio: float
    total_return: float
    max_drawdown: float
    win_rate: float
    total_trades: int
    best_train_ratio: float
    execution_time: float
    additional_metrics: Dict[str, Any] = field(default_factory=dict)

    def to_dict(self) -> Dict:
        """转换为字典"""
        return {
            'feature_set': self.feature_set,
            'test_name': self.test_name,
            'sharpe_ratio': self.sharpe_ratio,
            'total_return': self.total_return,
            'max_drawdown': self.max_drawdown,
            'win_rate': self.win_rate,
            'total_trades': self.total_trades,
            'best_train_ratio': self.best_train_ratio,
            'execution_time': self.execution_time,
            **self.additional_metrics
        }

class StandardizedBacktestEngine:
    """标准化回测引擎"""

    def __init__(self, config: SystemConfig, main_config=None):
        self.config = config
        self.main_config = main_config or load_config()
        self.connected = False

    def connect_mt5(self) -> bool:
        """连接MT5"""
        if not HMM_AVAILABLE:
            return False

        if not mt5.initialize(
            path=self.main_config.mt5_path,
            login=self.main_config.login_id,
            password=self.main_config.password,
            server=self.main_config.server_name,
            timeout=self.main_config.timeout
        ):
            print(f"❌ MT5初始化失败: {mt5.last_error()}")
            return False

        self.connected = True
        account_info = mt5.account_info()
        print(f"✅ MT5连接成功 - 账户: {account_info.login}")
        return True

    def disconnect(self):
        """断开MT5连接"""
        if self.connected:
            mt5.shutdown()
            self.connected = False

    def get_data(self) -> pd.DataFrame:
        """获取历史数据"""
        if not self.connected:
            return pd.DataFrame()

        end_date = datetime.now(timezone.utc)
        start_date = end_date - timedelta(days=self.config.lookback_days)

        rates = mt5.copy_rates_range(
            self.config.symbol,
            getattr(mt5, f"TIMEFRAME_{self.config.timeframe}"),
            start_date,
            end_date
        )

        if rates is None or len(rates) == 0:
            return pd.DataFrame()

        df = pd.DataFrame(rates)
        df['time'] = pd.to_datetime(df['time'], unit='s')
        df.set_index('time', inplace=True)
        df.columns = ['Open', 'High', 'Low', 'Close', 'TickVolume', 'Spread', 'RealVolume']

        return df

    def run_single_backtest(self, data: pd.DataFrame, features: List[str],
                           test_name: str = "") -> Optional[BacktestResult]:
        """运行单个回测"""
        start_time = time.time()

        try:
            # 数据验证
            clean_data = data[features + ['log_return']].dropna()
            if len(clean_data) < self.config.min_train_samples:
                return None

            best_sharpe = float('-inf')
            best_result = None

            # 前向分析
            for train_ratio in self.config.train_ratios:
                split_idx = int(len(clean_data) * train_ratio)
                train_data = clean_data.iloc[:split_idx]
                test_data = clean_data.iloc[split_idx:]

                if len(test_data) < self.config.min_test_samples:
                    continue

                # 训练HMM模型
                result = self._train_and_test_hmm(train_data, test_data, features)
                if result and result['sharpe_ratio'] > best_sharpe:
                    best_sharpe = result['sharpe_ratio']
                    best_result = result
                    best_result['train_ratio'] = train_ratio

            if best_result is None:
                return None

            execution_time = time.time() - start_time

            return BacktestResult(
                feature_set=features,
                test_name=test_name,
                sharpe_ratio=best_result['sharpe_ratio'],
                total_return=best_result['total_return'],
                max_drawdown=best_result['max_drawdown'],
                win_rate=best_result['win_rate'],
                total_trades=best_result['total_trades'],
                best_train_ratio=best_result['train_ratio'],
                execution_time=execution_time,
                additional_metrics=best_result.get('additional_metrics', {})
            )

        except Exception as e:
            print(f"❌ 回测失败 ({test_name}): {e}")
            return None

    def _train_and_test_hmm(self, train_data: pd.DataFrame, test_data: pd.DataFrame,
                           features: List[str]) -> Optional[Dict]:
        """训练和测试HMM模型"""
        try:
            # 训练HMM
            X_train = train_data[features].values
            scaler = StandardScaler().fit(X_train)
            X_train_scaled = scaler.transform(X_train)

            model = GaussianHMM(
                n_components=self.config.n_states,
                covariance_type=self.config.covariance_type,
                random_state=self.config.random_state,
                n_iter=self.config.n_iter
            )
            model.fit(X_train_scaled)

            # 状态映射
            states = model.predict(X_train_scaled)
            state_returns = {}
            for state in range(self.config.n_states):
                mask = states == state
                if np.sum(mask) > 0:
                    state_returns[state] = np.mean(train_data['feature_log_return'].iloc[mask])

            sorted_states = sorted(state_returns.items(), key=lambda x: x[1])
            if len(sorted_states) < 3:
                return None

            state_map = {
                sorted_states[0][0]: "下跌",
                sorted_states[1][0]: "盘整",
                sorted_states[2][0]: "上涨"
            }

            # 测试预测
            X_test = test_data[features].values
            X_test_scaled = scaler.transform(X_test)
            test_states = model.predict(X_test_scaled)

            # 生成交易信号
            test_signals = pd.Series(test_states).map({
                k: 1 if v == "上涨" else (-1 if v == "下跌" else 0)
                for k, v in state_map.items()
            }).shift(1).fillna(0).values

            # 计算性能指标
            test_returns = test_data['log_return'].values
            strategy_returns = test_returns * test_signals[:len(test_returns)]

            return self._calculate_metrics(strategy_returns, test_signals)

        except Exception as e:
            print(f"❌ HMM训练测试失败: {e}")
            return None

    def _calculate_metrics(self, strategy_returns: np.ndarray, signals: np.ndarray) -> Dict:
        """计算性能指标"""
        if len(strategy_returns) == 0 or np.std(strategy_returns) == 0:
            return None

        # 基础统计
        total_return = np.sum(strategy_returns)
        mean_return = np.mean(strategy_returns)
        std_return = np.std(strategy_returns)

        # 交易次数
        signal_changes = np.sum(np.diff(signals) != 0)

        # 夏普比率
        if signal_changes > 0:
            avg_holding_periods = len(signals) / signal_changes
            minutes_per_year = 252 * 23 * 60
            trades_per_year = minutes_per_year / avg_holding_periods
            sharpe_ratio = mean_return / std_return * np.sqrt(trades_per_year)
        else:
            sharpe_ratio = mean_return / std_return * np.sqrt(252)

        # 最大回撤
        cumulative_returns = np.cumsum(strategy_returns)
        peak = np.maximum.accumulate(cumulative_returns)
        drawdown = (peak - cumulative_returns)
        max_drawdown = np.max(drawdown) * 100

        # 胜率
        positive_returns = np.sum(strategy_returns > 0)
        win_rate = positive_returns / len(strategy_returns) * 100

        return {
            'sharpe_ratio': sharpe_ratio,
            'total_return': total_return * 100,
            'max_drawdown': max_drawdown,
            'win_rate': win_rate,
            'total_trades': signal_changes,
            'mean_return': mean_return,
            'std_return': std_return,
            'additional_metrics': {
                'cumulative_return': np.exp(total_return) - 1,
                'avg_holding_periods': avg_holding_periods if signal_changes > 0 else 0
            }
        }

if __name__ == "__main__":
    # 测试特征库
    library = FeatureLibrary()
    print("🔧 特征库初始化完成")
    print(f"📊 总特征数: {len(library.features)}")
    print(f"📂 特征分类: {list(library.categories.keys())}")

    # 显示特征列表
    features_df = library.list_features()
    print("\n📋 特征列表:")
    print(features_df.to_string(index=False))
