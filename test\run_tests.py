#!/usr/bin/env python3
"""
HMM自动化交易系统测试执行脚本
按步骤运行所有测试，确保系统稳定可靠
"""

import sys
import os
import argparse
from datetime import datetime

def run_unit_tests():
    """运行单元测试"""
    print("🧪 第1步：运行单元测试")
    print("-" * 50)
    
    try:
        import subprocess
        result = subprocess.run([sys.executable, 'test_suite.py'], 
                              capture_output=True, text=True, cwd=os.getcwd())
        
        print(result.stdout)
        if result.stderr:
            print("错误输出:", result.stderr)
        
        success = result.returncode == 0
        print(f"\n单元测试结果: {'✅ 通过' if success else '❌ 失败'}")
        return success
        
    except Exception as e:
        print(f"❌ 单元测试执行异常: {e}")
        return False

def run_strategy_comparison():
    """运行策略一致性对比"""
    print("\n🔍 第2步：策略逻辑一致性验证")
    print("-" * 50)
    
    try:
        import subprocess
        result = subprocess.run([sys.executable, 'strategy_comparison.py'], 
                              capture_output=True, text=True, cwd=os.getcwd())
        
        print(result.stdout)
        if result.stderr:
            print("错误输出:", result.stderr)
        
        success = result.returncode == 0
        print(f"\n策略对比结果: {'✅ 一致' if success else '❌ 不一致'}")
        return success
        
    except Exception as e:
        print(f"❌ 策略对比执行异常: {e}")
        return False

def run_dry_run_test():
    """运行试运行测试"""
    print("\n🏃 第3步：系统试运行测试")
    print("-" * 50)
    
    try:
        import subprocess
        result = subprocess.run([sys.executable, 'dry_run_test.py'], 
                              capture_output=True, text=True, cwd=os.getcwd())
        
        print(result.stdout)
        if result.stderr:
            print("错误输出:", result.stderr)
        
        success = result.returncode == 0
        print(f"\n试运行结果: {'✅ 成功' if success else '❌ 失败'}")
        return success
        
    except Exception as e:
        print(f"❌ 试运行测试执行异常: {e}")
        return False

def validate_configuration():
    """验证配置文件"""
    print("\n⚙️ 第4步：配置文件验证")
    print("-" * 50)
    
    try:
        from config_loader import load_config
        
        print("验证配置文件...")
        config = load_config('config.json')
        
        # 检查关键配置
        checks = [
            ("MT5登录ID", config.login_id != 12345678),  # 不是默认值
            ("MT5密码", config.password != "your_password"),  # 不是默认值
            ("交易品种", config.symbol in ["XAUUSD", "EURUSD", "GBPUSD"]),
            ("风险比例", 0 < config.risk_per_trade <= 0.05),  # 合理范围
            ("最大仓位", 0 < config.max_position_size <= 1.0),
            ("时区配置", config.local_timezone == "Asia/Shanghai")
        ]
        
        all_valid = True
        for check_name, is_valid in checks:
            status = "✅" if is_valid else "⚠️"
            print(f"  {status} {check_name}: {'正常' if is_valid else '需要检查'}")
            if not is_valid and check_name in ["MT5登录ID", "MT5密码"]:
                all_valid = False
        
        print(f"\n配置验证结果: {'✅ 通过' if all_valid else '⚠️ 需要调整'}")
        
        if not all_valid:
            print("\n💡 提示: 请在config.json中设置正确的MT5账户信息")
        
        return True  # 配置验证不阻止后续测试
        
    except Exception as e:
        print(f"❌ 配置验证异常: {e}")
        return False

def check_dependencies():
    """检查依赖项"""
    print("\n📦 第0步：检查依赖项")
    print("-" * 50)
    
    required_packages = [
        'pandas',
        'numpy', 
        'scikit-learn',
        'hmmlearn',
        'MetaTrader5',
        'pytz'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
            print(f"  ✅ {package}")
        except ImportError:
            print(f"  ❌ {package} (缺失)")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n❌ 缺失依赖项: {', '.join(missing_packages)}")
        print("请运行: pip install " + " ".join(missing_packages))
        return False
    else:
        print(f"\n✅ 所有依赖项已安装")
        return True

def generate_test_summary(test_results):
    """生成测试总结报告"""
    print("\n" + "=" * 80)
    print("📊 测试总结报告")
    print("=" * 80)
    
    test_names = [
        "依赖项检查",
        "单元测试",
        "策略一致性验证", 
        "配置文件验证",
        "系统试运行测试"
    ]
    
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"测试环境: Python {sys.version.split()[0]}")
    print()
    
    passed_count = 0
    for i, (test_name, result) in enumerate(zip(test_names, test_results)):
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{i+1}. {test_name}: {status}")
        if result:
            passed_count += 1
    
    print(f"\n总体结果: {passed_count}/{len(test_results)} 通过")
    
    if all(test_results):
        print("\n🎉 所有测试通过！系统已准备就绪。")
        print("\n🚀 您现在可以开始模拟交易：")
        print("   python run_trader.py --mode paper --symbol XAUUSD")
        print("\n💡 建议：")
        print("   1. 先运行2-4小时观察系统表现")
        print("   2. 检查日志文件确认无异常")
        print("   3. 确认信号生成和风险控制正常")
        print("   4. 满意后再考虑实盘交易")
        return True
    else:
        print("\n⚠️ 部分测试未通过，请解决问题后重新测试。")
        print("\n🔧 故障排除建议：")
        
        if not test_results[0]:
            print("   - 安装缺失的Python包")
        if not test_results[1]:
            print("   - 检查代码文件完整性")
        if not test_results[2]:
            print("   - 检查策略逻辑是否正确实现")
        if not test_results[3]:
            print("   - 设置正确的MT5账户信息")
        if not test_results[4]:
            print("   - 检查MT5连接和数据获取")
        
        return False

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='运行HMM自动化交易系统完整测试')
    parser.add_argument('--skip-unit', action='store_true', help='跳过单元测试')
    parser.add_argument('--skip-strategy', action='store_true', help='跳过策略对比测试')
    parser.add_argument('--skip-dry-run', action='store_true', help='跳过试运行测试')
    parser.add_argument('--only', choices=['unit', 'strategy', 'dry-run', 'config'], 
                       help='只运行指定的测试')
    
    args = parser.parse_args()
    
    print("🚀 HMM自动化交易系统完整测试套件")
    print("=" * 80)
    print("这个测试将验证系统的各个组件是否正常工作")
    print()
    
    try:
        test_results = []
        
        # 检查依赖项
        deps_ok = check_dependencies()
        test_results.append(deps_ok)
        
        if not deps_ok:
            print("❌ 依赖项检查失败，无法继续测试")
            sys.exit(1)
        
        # 运行指定的单个测试
        if args.only:
            if args.only == 'unit':
                success = run_unit_tests()
            elif args.only == 'strategy':
                success = run_strategy_comparison()
            elif args.only == 'dry-run':
                success = run_dry_run_test()
            elif args.only == 'config':
                success = validate_configuration()
            
            print(f"\n测试结果: {'✅ 成功' if success else '❌ 失败'}")
            sys.exit(0 if success else 1)
        
        # 运行完整测试套件
        print("开始运行完整测试套件...")
        print("预计耗时: 2-5分钟")
        print()
        
        # 单元测试
        if not args.skip_unit:
            unit_result = run_unit_tests()
            test_results.append(unit_result)
        else:
            print("⏭️ 跳过单元测试")
            test_results.append(True)
        
        # 策略对比测试
        if not args.skip_strategy:
            strategy_result = run_strategy_comparison()
            test_results.append(strategy_result)
        else:
            print("⏭️ 跳过策略对比测试")
            test_results.append(True)
        
        # 配置验证
        config_result = validate_configuration()
        test_results.append(config_result)
        
        # 试运行测试
        if not args.skip_dry_run:
            dry_run_result = run_dry_run_test()
            test_results.append(dry_run_result)
        else:
            print("⏭️ 跳过试运行测试")
            test_results.append(True)
        
        # 生成总结报告
        success = generate_test_summary(test_results)
        
        sys.exit(0 if success else 1)
        
    except KeyboardInterrupt:
        print("\n👋 测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 测试执行异常: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main()