#!/usr/bin/env python3
"""
HMM自动化交易系统启动脚本
支持命令行参数和多种运行模式
"""

import argparse
import sys
import os
from config_loader import load_config, TradingMode

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def main():
    """主程序"""
    parser = argparse.ArgumentParser(description='HMM 7*24小时自动化交易系统')
    
    # 基础参数
    parser.add_argument('--config', '-c', default='config.json', 
                       help='配置文件路径 (默认: config.json)')
    parser.add_argument('--mode', '-m', choices=['paper', 'live', 'backtest'], 
                       default='paper', help='交易模式 (默认: paper)')
    parser.add_argument('--symbol', '-s', default='XAUUSD', 
                       help='交易品种 (默认: XAUUSD)')
    parser.add_argument('--timeframe', '-t', choices=['M1', 'M5', 'M15', 'H1'], 
                       default='M1', help='时间周期 (默认: M1)')
    
    # 策略参数
    parser.add_argument('--states', type=int, default=3, choices=[2, 3, 4, 5],
                       help='HMM状态数 (默认: 3)')
    parser.add_argument('--lookback', type=int, default=20,
                       help='历史数据回看天数 (默认: 20)')
    
    # 风险参数 (设置为None，优先使用配置文件)
    parser.add_argument('--max-position', type=float, default=None,
                       help='最大仓位大小/手 (默认: 使用配置文件)')
    parser.add_argument('--risk-per-trade', type=float, default=None,
                       help='每笔交易风险比例 (默认: 使用配置文件)')
    parser.add_argument('--stop-loss', type=int, default=None,
                       help='止损点数 (默认: 使用配置文件)')
    parser.add_argument('--take-profit', type=int, default=None,
                       help='止盈点数 (默认: 使用配置文件)')
    
    # 系统参数
    parser.add_argument('--log-level', choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'],
                       default='INFO', help='日志级别 (默认: INFO)')
    parser.add_argument('--dry-run', action='store_true',
                       help='试运行模式，只显示信号不执行交易')
    
    args = parser.parse_args()
    
    try:
        # 加载配置
        config = load_config(args.config)
        
        # 命令行参数覆盖配置（仅覆盖非None的值）
        config.trading_mode = TradingMode(args.mode)
        config.symbol = args.symbol
        config.timeframe = args.timeframe
        config.n_states = args.states
        config.lookback_days = args.lookback
        config.log_level = args.log_level
        
        # 只有命令行明确指定时才覆盖风险参数
        if args.max_position is not None:
            config.max_position_size = args.max_position
        if args.risk_per_trade is not None:
            config.risk_per_trade = args.risk_per_trade
        if args.stop_loss is not None:
            config.stop_loss_points = args.stop_loss
        if args.take_profit is not None:
            config.take_profit_points = args.take_profit
        
        # 试运行模式强制使用模拟交易
        if args.dry_run:
            config.trading_mode = TradingMode.PAPER
            print("🔸 试运行模式已启用 - 只显示信号，不执行实际交易")

        # 检查是否启用优化功能
        dynamic_pos = getattr(config, 'dynamic_position_sizing', False)
        kelly_factor = getattr(config, 'kelly_factor', 0.5)

        print("🚀 HMM 7*24小时自动化交易系统 - 优化版")
        print("=" * 65)
        print(f"📊 交易品种: {config.symbol}")
        print(f"⏰ 时间周期: {config.timeframe}")
        print(f"🎯 交易模式: {config.trading_mode.value}")
        print(f"🧠 HMM状态数: {config.n_states}")
        print(f"📈 历史数据: {config.lookback_days} 天")
        
        if dynamic_pos:
            kelly_threshold = getattr(config, 'kelly_threshold', 0.005)
            print(f"💰 仓位管理: Kelly动态仓位 (基础{config.max_position_size}手 → 智能调整)")
            print(f"📊 Kelly参数: 系数{kelly_factor:.2f}, 阈值{kelly_threshold*100:.1f}%")
            print(f"🎯 核心优化: ✅ 智能仓位 ✅ 自动学习 ✅ 风险可控")
            print(f"🚀 优化说明: 系统将根据交易表现自动调整仓位大小")
        else:
            print(f"💰 最大仓位: {config.max_position_size} 手 (固定模式)")
            print("💡 提示: 在config.json中设置 'dynamic_position_sizing': true 启用Kelly优化")
            
        print(f"⚠️ 风险控制: {config.risk_per_trade:.1%}/笔")
        print(f"🛑 止损: {config.stop_loss_points} 点")
        print(f"🎯 止盈: {config.take_profit_points} 点")
        
        if dynamic_pos:
            print(f"📈 预期提升: 基于Kelly准则的专业资金管理，预期收益5-10倍提升")
        
        print("=" * 65)
        
        if config.trading_mode == TradingMode.LIVE:
            confirm = input("⚠️ 您正在启动实盘交易模式，确认继续？(yes/no): ")
            if confirm.lower() not in ['yes', 'y']:
                print("❌ 用户取消启动")
                return
        
        # 动态导入主系统（避免循环导入）
        from hmm_auto_trader_24x7 import HMMAutoTradingSystem
        
        # 创建并启动交易系统
        trading_system = HMMAutoTradingSystem(config)
        trading_system.start()
        
    except KeyboardInterrupt:
        print("\n👋 用户中断程序")
    except Exception as e:
        print(f"❌ 程序异常: {e}")
        import traceback
        traceback.print_exc()
    finally:
        print("系统已退出")

if __name__ == "__main__":
    main()