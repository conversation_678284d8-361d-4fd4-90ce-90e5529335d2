#!/usr/bin/env python3
"""
配置加载器 - 从JSON文件加载交易系统配置
支持环境变量覆盖和配置验证
"""

import json
import os
from dataclasses import dataclass
from typing import Dict, Any
from enum import Enum

class TradingMode(Enum):
    """交易模式"""
    BACKTEST = "backtest"
    PAPER = "paper" 
    LIVE = "live"

@dataclass
class TradingConfig:
    """交易配置类"""
    # MT5连接配置 - 使用配置文件，不再硬编码
    mt5_path: str = ""  # 从配置文件加载
    login_id: int = 0  # 从配置文件加载
    password: str = ""  # 从配置文件加载
    server_name: str = ""  # 从配置文件加载
    timeout: int = 60000
    
    # 交易配置
    symbol: str = "XAUUSD"
    timeframe: str = "M1"
    trading_mode: TradingMode = TradingMode.PAPER
    
    # 策略参数
    n_states: int = 3
    ma_fast: int = 20
    ma_slow: int = 35
    lookback_days: int = 20
    min_train_ratio: float = 0.6
    max_train_ratio: float = 0.8
    
    # 风险管理
    max_position_size: float = 1.0
    risk_per_trade: float = 0.02
    max_daily_loss: float = 0.05
    max_drawdown: float = 0.10
    enable_stop_loss: bool = False
    enable_take_profit: bool = False
    stop_loss_points: int = 100
    take_profit_points: int = 200
    
    # 优化功能
    dynamic_position_sizing: bool = False
    kelly_factor: float = 0.5
    kelly_threshold: float = 0.02
    kelly_directional_threshold: float = 0.06
    confidence_threshold: float = 0.7
    low_confidence_factor: float = 0.5
    position_change_threshold: float = 0.005
    significant_change_threshold: float = 0.01
    atr_stop_loss: bool = False
    
    # 系统参数
    reconnect_attempts: int = 5
    reconnect_delay: int = 30
    heartbeat_interval: int = 60
    data_update_interval: int = 10
    log_level: str = "INFO"

    # 交易重试参数
    max_trade_retries: int = 3
    trade_retry_delays: list = None  # [5, 15, 30]
    max_time_sync_diff: int = 14400  # 4小时
    
    # 时区配置
    local_timezone: str = 'Asia/Shanghai'
    server_timezone: str = 'Europe/Athens'
    
    # 预警配置
    enable_email: bool = False
    enable_telegram: bool = False
    email: str = ""
    telegram_token: str = ""
    telegram_chat_id: str = ""

def load_config(config_file: str = "config.json") -> TradingConfig:
    """
    加载配置文件
    支持环境变量覆盖敏感信息
    """
    # 默认配置
    config = TradingConfig()
    
    try:
        # 加载JSON配置文件
        if os.path.exists(config_file):
            with open(config_file, 'r', encoding='utf-8') as f:
                config_data = json.load(f)
            
            # 更新配置
            if 'mt5_connection' in config_data:
                mt5_config = config_data['mt5_connection']
                # 如果配置文件没有指定路径，使用标准默认路径
                default_path = "C:\\Program Files\\MetaTrader 5\\terminal64.exe"
                config.mt5_path = mt5_config.get('path', default_path)
                config.login_id = int(mt5_config.get('login', config.login_id))
                config.password = mt5_config.get('password', config.password)
                config.server_name = mt5_config.get('server', config.server_name)
                config.timeout = int(mt5_config.get('timeout', config.timeout))
            
            if 'trading' in config_data:
                trading_config = config_data['trading']
                config.symbol = trading_config.get('symbol', config.symbol)
                config.timeframe = trading_config.get('timeframe', config.timeframe)
                mode_str = trading_config.get('trading_mode', config.trading_mode.value)
                config.trading_mode = TradingMode(mode_str)
            
            if 'strategy' in config_data:
                strategy_config = config_data['strategy']
                config.n_states = int(strategy_config.get('n_states', config.n_states))
                config.ma_fast = int(strategy_config.get('ma_fast', config.ma_fast))
                config.ma_slow = int(strategy_config.get('ma_slow', config.ma_slow))
                config.lookback_days = int(strategy_config.get('lookback_days', config.lookback_days))
                config.min_train_ratio = float(strategy_config.get('min_train_ratio', config.min_train_ratio))
                config.max_train_ratio = float(strategy_config.get('max_train_ratio', config.max_train_ratio))
            
            if 'risk_management' in config_data:
                risk_config = config_data['risk_management']
                config.max_position_size = float(risk_config.get('max_position_size', config.max_position_size))
                config.risk_per_trade = float(risk_config.get('risk_per_trade', config.risk_per_trade))
                config.max_daily_loss = float(risk_config.get('max_daily_loss', config.max_daily_loss))
                config.max_drawdown = float(risk_config.get('max_drawdown', config.max_drawdown))
                config.enable_stop_loss = bool(risk_config.get('enable_stop_loss', config.enable_stop_loss))
                config.enable_take_profit = bool(risk_config.get('enable_take_profit', config.enable_take_profit))
                config.stop_loss_points = int(risk_config.get('stop_loss_points', config.stop_loss_points))
                config.take_profit_points = int(risk_config.get('take_profit_points', config.take_profit_points))
                
                # 新增优化配置项
                config.dynamic_position_sizing = bool(risk_config.get('dynamic_position_sizing', False))
                config.kelly_factor = float(risk_config.get('kelly_factor', 0.5))
                config.kelly_threshold = float(risk_config.get('kelly_threshold', 0.005))
                config.kelly_directional_threshold = float(risk_config.get('kelly_directional_threshold', 0.06))
                config.atr_stop_loss = bool(risk_config.get('atr_stop_loss', False))
            
            if 'system' in config_data:
                system_config = config_data['system']
                config.reconnect_attempts = int(system_config.get('reconnect_attempts', config.reconnect_attempts))
                config.reconnect_delay = int(system_config.get('reconnect_delay', config.reconnect_delay))
                config.heartbeat_interval = int(system_config.get('heartbeat_interval', config.heartbeat_interval))
                config.data_update_interval = int(system_config.get('data_update_interval', config.data_update_interval))
                config.log_level = system_config.get('log_level', config.log_level)

                # 交易重试参数
                config.max_trade_retries = int(system_config.get('max_trade_retries', config.max_trade_retries))
                config.trade_retry_delays = system_config.get('trade_retry_delays', [5, 15, 30])
                config.max_time_sync_diff = int(system_config.get('max_time_sync_diff', config.max_time_sync_diff))
            
            if 'timezone' in config_data:
                timezone_config = config_data['timezone']
                config.local_timezone = timezone_config.get('local_timezone', config.local_timezone)
                config.server_timezone = timezone_config.get('server_timezone', config.server_timezone)
            
            if 'alerts' in config_data:
                alerts_config = config_data['alerts']
                config.enable_email = alerts_config.get('enable_email', config.enable_email)
                config.enable_telegram = alerts_config.get('enable_telegram', config.enable_telegram)
                config.email = alerts_config.get('email', config.email)
                config.telegram_token = alerts_config.get('telegram_token', config.telegram_token)
                config.telegram_chat_id = alerts_config.get('telegram_chat_id', config.telegram_chat_id)
        
        else:
            print(f"⚠️ 配置文件 {config_file} 不存在，使用默认配置")
        
        # 环境变量覆盖（安全考虑）
        config.login_id = int(os.getenv('MT5_LOGIN', config.login_id))
        config.password = os.getenv('MT5_PASSWORD', config.password)
        config.server_name = os.getenv('MT5_SERVER', config.server_name)
        
        # 配置验证
        validate_config(config)
        
        print(f"✅ 配置加载完成")
        
        return config
        
    except Exception as e:
        print(f"❌ 配置加载失败: {e}")
        print("使用默认配置")
        return TradingConfig()

def validate_config(config: TradingConfig):
    """配置验证"""
    errors = []
    
    # 基础验证
    if config.login_id <= 0:
        errors.append("MT5账户ID必须为正整数")
    
    if not config.password:
        errors.append("MT5密码不能为空")
    
    if config.symbol not in ["XAUUSD", "EURUSD", "GBPUSD", "USDJPY", "AUDUSD", "USDCAD", "NZDUSD", "USDCHF"]:
        print(f"⚠️ 交易品种 {config.symbol} 可能不受支持")
    
    if config.timeframe not in ["M1", "M5", "M15", "H1"]:
        errors.append(f"不支持的时间周期: {config.timeframe}")
    
    # 风险参数验证
    if config.max_position_size <= 0 or config.max_position_size > 10:
        errors.append("最大仓位必须在0-10手之间")
    
    if config.risk_per_trade <= 0 or config.risk_per_trade > 0.1:
        errors.append("每笔风险比例必须在0-10%之间")
    
    if config.max_daily_loss <= 0 or config.max_daily_loss > 0.2:
        errors.append("最大日亏损必须在0-20%之间")
    
    if config.max_drawdown <= 0 or config.max_drawdown > 0.5:
        errors.append("最大回撤必须在0-50%之间")
    
    # 策略参数验证
    if config.n_states < 2 or config.n_states > 5:
        errors.append("HMM状态数必须在2-5之间")
    
    if config.ma_fast >= config.ma_slow:
        errors.append("快速移动平均必须小于慢速移动平均")
    
    if config.lookback_days < 5 or config.lookback_days > 60:
        errors.append("回看天数必须在5-60天之间")
    
    if errors:
        print("❌ 配置验证失败:")
        for error in errors:
            print(f"  - {error}")
        raise ValueError("配置验证失败")
    
    print("✅ 配置验证通过")

def save_config_template(filename: str = "config_template.json"):
    """保存配置模板"""
    template = {
        "mt5_connection": {
            "path": "C:\\Program Files\\MetaTrader 5\\terminal64.exe",
            "login": 12345678,
            "password": "your_password",
            "server": "MetaQuotes-Demo",
            "timeout": 60000,
            "comment": "MT5连接配置"
        },
        
        "trading": {
            "symbol": "XAUUSD",
            "timeframe": "M1",
            "trading_mode": "paper",
            "comment": "交易配置 - 模式: paper(模拟)/live(实盘)/backtest(回测)"
        },
        
        "strategy": {
            "n_states": 3,
            "ma_fast": 20,
            "ma_slow": 35,
            "lookback_days": 20,
            "min_train_ratio": 0.6,
            "max_train_ratio": 0.8,
            "comment": "HMM策略参数"
        },
        
        "risk_management": {
            "max_position_size": 1.0,
            "risk_per_trade": 0.02,
            "max_daily_loss": 0.05,
            "max_drawdown": 0.10,
            "stop_loss_points": 100,
            "take_profit_points": 200,
            "comment": "风险管理配置"
        },
        
        "system": {
            "reconnect_attempts": 5,
            "reconnect_delay": 30,
            "heartbeat_interval": 60,
            "data_update_interval": 10,
            "log_level": "INFO",
            "comment": "系统运行参数"
        },
        
        "timezone": {
            "local_timezone": "Asia/Shanghai",
            "server_timezone": "Europe/Athens",
            "comment": "时区配置"
        },
        
        "alerts": {
            "enable_email": false,
            "enable_telegram": false,
            "email": "<EMAIL>",
            "telegram_token": "your_bot_token",
            "telegram_chat_id": "your_chat_id",
            "comment": "预警通知配置"
        }
    }
    
    with open(filename, 'w', encoding='utf-8') as f:
        json.dump(template, f, indent=2, ensure_ascii=False)
    
    print(f"✅ 配置模板已保存到 {filename}")

if __name__ == "__main__":
    # 测试配置加载
    config = load_config()
    print("\n配置加载测试完成")
    
    # 生成配置模板
    save_config_template()