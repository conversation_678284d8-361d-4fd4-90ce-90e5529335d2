#!/usr/bin/env python3
"""
Simplified Dry-run Test - ASCII Version
Test core system functionality without complex MT5 mocking
"""

import sys
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from unittest.mock import Mock, patch, MagicMock

def test_system_components():
    """Test individual system components"""
    print("STEP 5: System Integration Test")
    print("=" * 40)
    
    test_results = []
    
    # Test 1: Configuration Loading
    try:
        from config_loader import load_config, TradingConfig
        config = load_config('config.json')
        test_results.append(("Config Loading", True))
        print("  OK Configuration loading")
    except Exception as e:
        test_results.append(("Config Loading", False))
        print(f"  FAILED Configuration loading: {e}")
    
    # Test 2: Data Manager Basic Functionality
    try:
        from hmm_auto_trader_24x7 import DataManager, TradingConfig
        
        config = TradingConfig()
        data_manager = DataManager(config, <PERSON><PERSON>(), <PERSON><PERSON>())
        
        # Test feature calculation with sample data
        np.random.seed(42)
        dates = pd.date_range('2024-12-01', periods=100, freq='h')
        prices = 2000 + np.cumsum(np.random.randn(100) * 0.1)
        
        df = pd.DataFrame({
            'Open': prices,
            'High': prices + abs(np.random.randn(100) * 0.1),
            'Low': prices - abs(np.random.randn(100) * 0.1),
            'Close': prices,
            'Volume': np.random.randint(1000, 5000, 100)
        }, index=dates)
        
        result_df = data_manager._calculate_features(df)
        features_ok = all(col in result_df.columns for col in data_manager.features)
        
        test_results.append(("Data Manager", features_ok))
        print(f"  {'OK' if features_ok else 'FAILED'} Data Manager functionality")
    except Exception as e:
        test_results.append(("Data Manager", False))
        print(f"  FAILED Data Manager: {e}")
    
    # Test 3: Strategy Engine Components
    try:
        from hmm_auto_trader_24x7 import HMMStrategyEngine, SignalType
        
        # Mock data manager
        mock_data_manager = Mock()
        mock_data_manager.historical_data = result_df.dropna()
        
        strategy = HMMStrategyEngine(config, mock_data_manager, Mock())
        strategy.features = ['log_return', 'momentum_5m', 'momentum_20m', 'price_position', 'ma_diff']
        
        # Test basic initialization
        strategy_ok = hasattr(strategy, 'model') and hasattr(strategy, 'scaler')
        test_results.append(("Strategy Engine", strategy_ok))
        print(f"  {'OK' if strategy_ok else 'FAILED'} Strategy Engine initialization")
    except Exception as e:
        test_results.append(("Strategy Engine", False))
        print(f"  FAILED Strategy Engine: {e}")
    
    # Test 4: Risk Manager
    try:
        from hmm_auto_trader_24x7 import RiskManager
        
        mock_conn = Mock()
        risk_manager = RiskManager(config, mock_conn, Mock())
        
        # Test basic risk calculations
        risk_ok = hasattr(risk_manager, 'config') and hasattr(risk_manager, 'daily_pnl')
        test_results.append(("Risk Manager", risk_ok))
        print(f"  {'OK' if risk_ok else 'FAILED'} Risk Manager initialization")
    except Exception as e:
        test_results.append(("Risk Manager", False))
        print(f"  FAILED Risk Manager: {e}")
    
    # Test 5: Trade Executor
    try:
        from hmm_auto_trader_24x7 import TradeExecutor
        
        trade_executor = TradeExecutor(config, mock_conn, risk_manager, Mock())
        
        executor_ok = hasattr(trade_executor, 'config') and hasattr(trade_executor, 'magic_number')
        test_results.append(("Trade Executor", executor_ok))
        print(f"  {'OK' if executor_ok else 'FAILED'} Trade Executor initialization")
    except Exception as e:
        test_results.append(("Trade Executor", False))
        print(f"  FAILED Trade Executor: {e}")
    
    # Test 6: Signal Generation Logic
    try:
        # Test signal enumeration
        from hmm_auto_trader_24x7 import SignalType
        
        signals = [SignalType.BUY, SignalType.SELL, SignalType.HOLD]
        signal_values = [signal.value for signal in signals]
        expected_values = [1, -1, 0]
        
        signal_logic_ok = signal_values == expected_values
        test_results.append(("Signal Logic", signal_logic_ok))
        print(f"  {'OK' if signal_logic_ok else 'FAILED'} Signal enumeration")
    except Exception as e:
        test_results.append(("Signal Logic", False))
        print(f"  FAILED Signal Logic: {e}")
    
    # Calculate results
    total_tests = len(test_results)
    passed_tests = sum(1 for _, result in test_results if result)
    
    print(f"\nTotal components tested: {total_tests}")
    print(f"Passed: {passed_tests}")
    print(f"Failed: {total_tests - passed_tests}")
    
    success_rate = passed_tests / total_tests if total_tests > 0 else 0
    overall_success = success_rate >= 0.8  # 80% pass rate
    
    print(f"Success rate: {success_rate:.1%}")
    print(f"RESULT: {'PASSED' if overall_success else 'FAILED'}")
    
    return overall_success

if __name__ == "__main__":
    try:
        success = test_system_components()
        sys.exit(0 if success else 1)
    except Exception as e:
        print(f"Test execution failed: {e}")
        sys.exit(1)