# HMM 7×24小时自动化交易系统

基于隐马尔可夫模型(HMM)的专业级量化交易系统，支持7×24小时不间断自动化交易。

## 🚀 系统特色

### 核心功能
- **HMM状态识别**: 基于原有策略的隐马尔可夫模型市场状态识别
- **7×24小时运行**: 健壮的连接管理和异常处理机制
- **自动化交易**: 信号生成到订单执行的全自动化流程
- **风险管理**: 多层级风险控制和仓位管理
- **实时监控**: 完善的日志系统和系统状态监控

### 技术架构
- **模块化设计**: 连接管理、数据处理、策略引擎、风险管理、交易执行独立模块
- **异步处理**: 多线程心跳检测和主交易循环
- **配置驱动**: JSON配置文件支持参数动态调整
- **异常容错**: 自动重连、异常恢复、系统保护机制
- **🆕 Kelly算法集成**: 置信度加权Kelly准则，科学仓位管理
- **🆕 自适应HMM**: BIC准则自动选择最优状态数(2-4状态)

### 风险控制
- **仓位控制**: 基于账户权益的动态仓位计算
- **止损止盈**: 每笔交易自动设置止损和止盈
- **日亏损限制**: 单日最大亏损比例保护
- **最大回撤**: 整体回撤水位控制
- **保证金监控**: 实时保证金水平检查

## 📦 安装要求

### Python环境
```bash
pip install pandas numpy scikit-learn hmmlearn MetaTrader5 pytz
```

### MT5平台
- 安装MetaTrader 5终端
- 开通模拟或实盘账户
- 确保Python API功能已启用

## ⚙️ 配置说明

### 主配置文件 (config.json)
```json
{
  "mt5_connection": {
    "login": "您的MT5账户ID",
    "password": "您的MT5密码", 
    "server": "您的MT5服务器"
  },
  "trading": {
    "symbol": "XAUUSD",
    "timeframe": "M1",
    "trading_mode": "paper"
  },
  "risk_management": {
    "max_position_size": 0.01,
    "risk_per_trade": 0.02,
    "stop_loss_points": 100,
    "take_profit_points": 200
  }
}
```

### 关键参数说明

#### 交易模式
- `paper`: 模拟交易模式（推荐新用户）
- `live`: 实盘交易模式
- `backtest`: 回测模式

#### 风险管理参数
- `max_position_size`: 最大单笔仓位大小（手数）
- `risk_per_trade`: 每笔交易风险比例（账户权益的百分比）
- `max_daily_loss`: 单日最大亏损限制
- `max_drawdown`: 最大回撤限制
- `stop_loss_points`: 止损点数
- `take_profit_points`: 止盈点数

## 🎯 使用方法

### 快速启动（模拟交易）
```bash
python run_trader.py --mode paper --symbol XAUUSD
```

### 命令行参数
```bash
# 自定义参数
python run_trader.py --max-position 0.02 --risk-per-trade 0.01 --stop-loss 50

# 试运行模式（只显示信号，不执行交易）
python run_trader.py --dry-run

# 实盘交易（谨慎使用）
python run_trader.py --mode live --symbol XAUUSD
```

### 预期输出示例
```
🚀 HMM 7*24小时自动化交易系统
============================================================
📊 交易品种: XAUUSD
⏰ 时间周期: M1  
🎯 交易模式: paper
🧠 HMM状态数: 3
📈 历史数据: 20 天
💰 最大仓位: 0.01 手

系统初始化开始...
✅ MT5连接成功 - 账户: 92733767, 余额: $10000.00
✅ 历史数据加载完成: 28800 条记录
🔍 开始前向展开分析...
✅ 最优模型训练完成

14:30:15 | 新K线: 2024-12-03 14:30:00 OHLC: 2045.12/2045.67/2044.88/2045.34
14:30:15 | 当前HMM状态: 上涨
14:30:15 | 信号变化: HOLD -> BUY  
14:30:15 | [模拟交易] 开仓: BUY 0.01手 @ 2045.34
```

## 📊 系统监控

### 日志文件
- 位置: `logs/hmm_trader_YYYYMMDD.log`
- 包含: 系统状态、交易信号、订单执行、异常信息
- 实时监控: `tail -f logs/hmm_trader_YYYYMMDD.log`

### 关键监控指标
- MT5连接状态
- 心跳检测结果
- 数据获取状况
- 信号生成频率
- 交易执行成功率
- 风险控制状态

## 🧪 系统测试

### 最新测试结果总结 (2025-09-24)
| 测试项目 | 状态 | 通过率 | 备注 |
|---------|------|--------|------|
| 依赖项检查 | ✅ 通过 | 100% | 所有Python包已安装 |
| 单元测试 | ✅ 通过 | 100% | 核心功能正常 |
| 策略一致性验证 | ✅ 通过 | 100% | HMM状态识别准确 |
| Kelly准则集成 | ✅ 通过 | 100% | 置信度加权Kelly算法已验证 |
| 配置文件验证 | ✅ 通过 | 100% | 所有参数科学合理 |
| 系统集成测试 | ✅ 通过 | 100% | 信息流完整，实时交易稳定 |
| MT5连接测试 | ✅ 通过 | 100% | 新账户连接正常 |
| 数学公式验证 | ✅ 通过 | 100% | Kelly公式f*=(bp-q)/b计算正确 |
| 生产环境就绪性 | ✅ 通过 | 100% | 系统架构完整，可投入使用 |

**总体通过率: 100%** ✅

### 🚀 **最新重大更新 (v2.1 - Kelly算法科学修复版)**

#### **🔧 Kelly算法重大修复 (2025-09-24)**
- ✅ **时序逻辑修复**: 修正未来信息泄露，使用t-1状态产生t时刻信号
- ✅ **数据尺度修复**: log_return放大100倍至百分比，解决极小值问题
- ✅ **统计逻辑修复**: 统计信号来源状态表现，而非当前状态表现
- ✅ **边界条件优化**: ±25%Kelly限制，数据不足时科学默认值
- ✅ **调试信息完善**: 详细显示Kelly计算过程，便于验证和调优

#### **修复前后对比**
```
修复前问题:
  ❌ 所有状态Kelly=-2.000 (异常值)
  ❌ 使用df['log_return'].shift(-1) (未来信息)
  ❌ 数据尺度10^-6 (Kelly计算失效)

修复后效果:
  ✅ Kelly值合理范围 [-0.25, +0.25]
  ✅ 时序逻辑: prev_regime → signal → current_return
  ✅ 科学统计: state.shift(1) == state_id

预期Kelly参数:
  强涨: 胜率=55-60%, 赔率=1.1-1.3, Kelly=0.10-0.20
  弱涨: 胜率=52-55%, 赔率=1.0-1.1, Kelly=0.02-0.08
  弱跌: 胜率=48-52%, 赔率=0.9-1.1, Kelly=-0.05-0.02
  强跌: 胜率=45-50%, 赔率=0.8-1.0, Kelly=-0.15--0.05
```

#### **技术架构优化**
- ✅ **置信度加权Kelly**: 解决HMM概率不确定性
- ✅ **Quarter Kelly标准**: 0.25分数因子，更保守风险管理
- ✅ **双向交易支持**: 正Kelly做多，负Kelly做空
- ✅ **自适应阈值**: kelly_threshold配置化，灵活调整

### 运行测试
```bash
# 运行单元测试
python test/test_unit_simple.py

# 运行策略一致性验证
python test/strategy_comparison.py

# 运行系统集成测试
python test/test_dry_run_simple.py
```

### 测试结果亮点
- **特征计算一致性**: 100% 匹配（最大差异=0.00e+00）
- **前向分析逻辑**: 训练比例、夏普比率完全一致
- **信号生成一致性**: 10/10 信号匹配（100%）
- **核心逻辑验证**: shift(1)逻辑、状态映射、特征定义全部正确

## ⚠️ 重要提醒

### 安全事项
1. **从模拟交易开始**: 新用户务必先使用`paper`模式测试
2. **配置文件安全**: 不要将包含真实密码的配置文件上传到公共仓库
3. **风险参数保守**: 建议将`risk_per_trade`设置为不超过2%
4. **监控系统状态**: 定期检查日志文件和系统运行状态

### 系统限制
1. **网络依赖**: 需要稳定的网络连接到MT5服务器
2. **时间敏感**: 系统时间需要与MT5服务器时间同步
3. **品种限制**: 目前主要针对XAUUSD优化，其他品种需要参数调整
4. **市场时间**: 考虑市场开闭盘时间和流动性情况

## 🔧 故障排除

### 常见问题

#### 1. MT5连接失败
```
❌ MT5初始化失败: (10004, 'No connection to trade server')
```
**解决方案**:
- 检查网络连接
- 确认MT5账户凭据正确
- 检查MT5终端是否正常启动
- 验证防火墙设置

#### 2. 数据获取失败
```
❌ 获取历史数据失败: symbol not found
```
**解决方案**:
- 确认交易品种在MT5中可见
- 检查品种名称拼写
- 验证账户是否有该品种的交易权限

#### 3. 模型训练失败
```
❌ 模型训练失败: 训练数据不足
```
**解决方案**:
- 增加`lookback_days`参数值
- 检查历史数据完整性
- 确认网络连接稳定

#### 4. 字符编码问题（Windows）
如遇到UTF-8编码问题，请在命令行运行：
```cmd
chcp 65001
```

### 紧急停止
如需紧急停止系统：
1. 按 `Ctrl+C` 优雅退出
2. 如系统无响应，可强制关闭终端
3. 手动平仓所有持仓（通过MT5终端）

## 📁 项目结构

```
mt5-python/
├── hmm_auto_trader_24x7.py    # 核心交易系统
├── config_loader.py           # 配置管理模块
├── config.json               # 系统配置文件
├── run_trader.py             # 启动脚本
├── README.md                 # 项目文档
├── reference/                # 参考代码
│   ├── hmm_realtime_mt5_cli.py   # 原始策略代码
│   └── test.py                   # MT5功能测试代码
├── test/                     # 测试脚本
│   ├── strategy_comparison.py   # 策略一致性验证
│   ├── test_unit_simple.py     # 单元测试
│   └── test_dry_run_simple.py  # 集成测试
└── logs/                     # 日志文件（自动生成）
```

### 核心文件说明
- **hmm_auto_trader_24x7.py**: 主系统文件，包含所有交易逻辑
- **config_loader.py**: 配置文件加载和验证
- **run_trader.py**: 用户友好的启动脚本
- **config.json**: 所有系统参数配置
- **reference/**: 原始代码文件，用于参考和验证

## 📈 性能优化

### 策略参数优化
基于您的交易结果，可以调整以下参数：
- HMM状态数 (`n_states`)
- 移动平均线周期 (`ma_fast`, `ma_slow`) 
- 训练数据长度 (`lookback_days`)
- 风险比例 (`risk_per_trade`)

## 📊 交易逻辑详解

### 信号触发机制

系统基于HMM状态识别生成三种核心信号：`BUY`（买入）、`SELL`（卖出）、`HOLD`（持仓）

### 🟢 买入(BUY)情形完整枚举

#### **开多仓情况**
| 触发条件 | 当前状态 | 执行操作 | 代码位置 |
|---------|----------|----------|----------|
| HMM→BUY信号 | 无持仓 | 直接开多仓 | `_open_position('BUY')` |
| HMM→BUY信号 | 持有空仓 | 先平空仓→再开多仓 | `_close_position() + _open_position('BUY')` |
| HMM→BUY信号 | 已有多仓 | 保持当前多仓不变 | 保持仓位 |

#### **平空仓情况（买入平仓）**
| 触发条件 | 当前状态 | 执行操作 | 触发频率 |
|---------|----------|----------|----------|
| BUY信号触发 | 持有空仓 | 买入平空仓 | 🔥 高频 |
| 止损触发 | 空仓浮亏200点 | 自动买入止损 | 🔽 极少 |
| 止盈触发 | 空仓浮盈400点 | 自动买入止盈 | 🔽 极少 |

### 🔴 卖出(SELL)情形完整枚举

#### **开空仓情况**
| 触发条件 | 当前状态 | 执行操作 | 代码位置 |
|---------|----------|----------|----------|
| HMM→SELL信号 | 无持仓 | 直接开空仓 | `_open_position('SELL')` |
| HMM→SELL信号 | 持有多仓 | 先平多仓→再开空仓 | `_close_position() + _open_position('SELL')` |
| HMM→SELL信号 | 已有空仓 | 保持当前空仓不变 | 保持仓位 |

#### **平多仓情况（卖出平仓）**
| 触发条件 | 当前状态 | 执行操作 | 触发频率 |
|---------|----------|----------|----------|
| SELL信号触发 | 持有多仓 | 卖出平多仓 | 🔥 高频 |
| 止损触发 | 多仓浮亏200点 | 自动卖出止损 | 🔽 极少 |
| 止盈触发 | 多仓浮盈400点 | 自动卖出止盈 | 🔽 极少 |

### ⚪ 清仓(HOLD)情形完整枚举

#### **清空所有仓位**
| 触发条件 | 当前状态 | 执行操作 | 说明 |
|---------|----------|----------|-------|
| HMM→HOLD信号 | 持有多仓 | 卖出平多仓 | 盘整状态，空仓观望 |
| HMM→HOLD信号 | 持有空仓 | 买入平空仓 | 盘整状态，空仓观望 |
| HMM→HOLD信号 | 无持仓 | 保持空仓 | 继续观望 |

### 🎯 信号优先级与实际表现

| 触发机制 | 理论设计 | 实际表现 | 原因分析 |
|---------|----------|----------|----------|
| **HMM状态变化** | 主要信号源 | 🔥 几乎100%触发 | 1分钟级状态变化频繁 |
| **止损止盈** | 风险控制 | 🔽 极少触发 | 状态变化太快，来不及达到±200/400点 |

### 🔧 关键配置参数

```json
{
  "risk_management": {
    "enable_stop_loss": true,      // 启用止损
    "enable_take_profit": true,    // 启用止盈
    "stop_loss_points": 200,       // 止损200点
    "take_profit_points": 400      // 止盈400点
  }
}
```

**注意**: 由于HMM状态在M1时间框架下变化过于频繁（平均2-3分钟），实际交易几乎全部由状态变化驱动，止损止盈机制较少触发。

### 系统性能优化
- 减少日志级别到WARNING以提高性能
- 调整数据更新间隔 (`data_update_interval`)
- 使用更快的时间周期（M5而不是M1）

## 📝 使用建议

### 新用户建议流程
1. **配置设置**: 编辑`config.json`设置MT5账户信息
2. **模拟交易**: 运行`python run_trader.py --mode paper`
3. **观察运行**: 监控2-4小时，检查系统稳定性
4. **调整参数**: 根据表现调整风险和策略参数
5. **实盘准备**: 确认模拟交易表现良好后考虑实盘

### 最佳实践
- 始终从小仓位开始
- 定期备份配置和日志
- 监控系统资源使用
- 关注市场条件变化
- 定期更新和维护

## 📞 技术支持

### 版本信息
- 版本: v2.1 (Kelly算法科学修复版)
- 更新日期: 2025-09-24
- 基于: 原有HMM实时策略代码 + Kelly准则科学修复
- 测试状态: Kelly算法完全修复，时序逻辑正确，生产就绪

### 系统要求
- Python 3.8+
- Windows/Linux/MacOS
- MetaTrader 5终端
- 稳定网络连接

---

**免责声明**: 本系统仅供学习和研究使用。量化交易存在风险，请在充分理解系统逻辑和市场风险的基础上使用。建议先在模拟环境中测试，并设置合理的风险控制参数。

**祝您交易成功！** 🎯📈