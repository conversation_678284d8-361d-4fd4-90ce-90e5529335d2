#!/usr/bin/env python3
"""
统一特征测试器 - 整合现有工具的统一接口
=====================================

这个脚本整合了现有的所有特征测试工具，提供统一的接口：
1. 整合 hmm_backtest_v0.py 的基准测试
2. 整合 feature_ablation_test.py 的消融测试
3. 整合 feature_selector_smart.py 的智能选择
4. 整合 feature_selector_full.py 的完整框架
5. 提供新的系统化特征选择框架

作者: Augment Agent
日期: 2025-09-27
"""

import os
import sys
import warnings
import argparse
from pathlib import Path
from typing import Dict, Any

# 添加路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

warnings.filterwarnings('ignore')

def run_v0_baseline():
    """运行v0基准测试"""
    print("🚀 运行v0基准测试...")
    try:
        from hmm_backtest_v0 import main as v0_main
        v0_main()
        return True
    except Exception as e:
        print(f"❌ v0基准测试失败: {e}")
        return False

def run_v1_optimal():
    """运行v1优化测试"""
    print("🚀 运行v1优化测试...")
    try:
        from hmm_backtest_v1_optimal import HMMBacktestV1Optimal
        from config_loader import load_config
        
        main_config = load_config()
        tester = HMMBacktestV1Optimal(main_config)
        tester.run_backtest()
        return True
    except Exception as e:
        print(f"❌ v1优化测试失败: {e}")
        return False

def run_ablation_test():
    """运行特征消融测试"""
    print("🔬 运行特征消融测试...")
    try:
        from feature_ablation_test import main as ablation_main
        ablation_main()
        return True
    except Exception as e:
        print(f"❌ 消融测试失败: {e}")
        return False

def run_smart_selector():
    """运行智能特征选择"""
    print("🧠 运行智能特征选择...")
    try:
        from feature_selector_smart import SmartFeatureSelector
        from config_loader import load_config
        
        main_config = load_config()
        selector = SmartFeatureSelector(main_config)
        
        if selector.connect_mt5():
            data = selector.get_data()
            if not data.empty:
                data = selector.build_feature_library(data)
                result = selector.smart_feature_search(data, selector.get_baseline_features())
                print("✅ 智能选择完成")
                return True
        return False
    except Exception as e:
        print(f"❌ 智能选择失败: {e}")
        return False

def run_full_selector():
    """运行完整特征选择框架"""
    print("🔧 运行完整特征选择框架...")
    try:
        from feature_selector_full import FullFeatureSelector
        from config_loader import load_config
        
        main_config = load_config()
        selector = FullFeatureSelector(main_config)
        
        if selector.connect_mt5():
            data = selector.get_data()
            if not data.empty:
                data = selector.calculate_comprehensive_features(data)
                baseline_features = ['feature_log_return', 'momentum_5m', 'momentum_20m', 'price_position', 'ma_diff']
                result = selector.run_feature_selection(data, baseline_features)
                print("✅ 完整框架测试完成")
                return True
        return False
    except Exception as e:
        print(f"❌ 完整框架测试失败: {e}")
        return False

def run_new_system():
    """运行新的系统化特征选择框架"""
    print("🆕 运行新系统化特征选择框架...")
    try:
        from feature_selection_controller import FeatureSelectionController
        
        controller = FeatureSelectionController()
        result = controller.run_ablation_study()
        
        if 'error' not in result:
            print("✅ 新系统测试完成")
            print(f"📊 测试结果: {result}")
            return True
        else:
            print(f"❌ 新系统测试失败: {result['error']}")
            return False
    except Exception as e:
        print(f"❌ 新系统测试失败: {e}")
        return False

def compare_all_methods():
    """对比所有方法的结果"""
    print("\n📊 对比所有特征选择方法")
    print("=" * 60)
    
    methods = [
        ("v0基准测试", "hmm_backtest_v0.py", "基础5特征回测"),
        ("v1优化测试", "hmm_backtest_v1_optimal.py", "v0+趋势延续特征"),
        ("特征消融测试", "feature_ablation_test.py", "单特征边际贡献分析"),
        ("智能特征选择", "feature_selector_smart.py", "基于重要性的智能搜索"),
        ("完整选择框架", "feature_selector_full.py", "多算法融合选择"),
        ("新系统框架", "feature_selection_system.py", "统一的系统化框架")
    ]
    
    print("| 方法 | 文件 | 描述 |")
    print("|------|------|------|")
    for name, file, desc in methods:
        print(f"| {name} | {file} | {desc} |")
    
    print(f"\n🔄 方法演进路径:")
    print("v0基准 → v1优化 → 消融测试 → 智能选择 → 完整框架 → 新系统")
    
    print(f"\n✨ 新系统优势:")
    print("• 统一的特征库管理")
    print("• 标准化的回测流程")
    print("• 科学的统计分析")
    print("• 自动化的测试流程")
    print("• 可视化的结果展示")

def create_migration_guide():
    """创建迁移指南"""
    print("\n📋 迁移指南")
    print("=" * 60)
    
    print("🔄 从现有工具迁移到新系统:")
    
    migrations = [
        {
            "from": "hmm_backtest_v0.py",
            "to": "python run_feature_selection.py --mode ablation",
            "benefit": "统一接口，更好的分析"
        },
        {
            "from": "feature_ablation_test.py", 
            "to": "python run_feature_selection.py --mode ablation",
            "benefit": "科学的统计分析"
        },
        {
            "from": "feature_selector_smart.py",
            "to": "python run_feature_selection.py --mode smart --strategy greedy",
            "benefit": "更智能的搜索策略"
        },
        {
            "from": "feature_selector_full.py",
            "to": "python run_feature_selection.py --mode smart --strategy exhaustive",
            "benefit": "更完整的算法支持"
        }
    ]
    
    for migration in migrations:
        print(f"\n📤 {migration['from']}")
        print(f"📥 {migration['to']}")
        print(f"💡 优势: {migration['benefit']}")
    
    print(f"\n🎯 推荐迁移步骤:")
    print("1. 先运行演示: python demo_feature_selection.py")
    print("2. 查看特征库: python run_feature_selection.py --list-features")
    print("3. 运行消融测试: python run_feature_selection.py --mode ablation")
    print("4. 对比结果与现有工具")
    print("5. 逐步迁移到新系统")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="统一特征测试器")
    parser.add_argument('--method', choices=['v0', 'v1', 'ablation', 'smart', 'full', 'new', 'all'], 
                       default='all', help='选择测试方法')
    parser.add_argument('--compare', action='store_true', help='对比所有方法')
    parser.add_argument('--migrate', action='store_true', help='显示迁移指南')
    
    args = parser.parse_args()
    
    print("🔧 统一特征测试器")
    print("=" * 60)
    print("整合所有现有特征选择工具的统一接口")
    print("=" * 60)
    
    if args.compare:
        compare_all_methods()
        return
    
    if args.migrate:
        create_migration_guide()
        return
    
    results = {}
    
    if args.method == 'all' or args.method == 'v0':
        results['v0'] = run_v0_baseline()
    
    if args.method == 'all' or args.method == 'v1':
        results['v1'] = run_v1_optimal()
    
    if args.method == 'all' or args.method == 'ablation':
        results['ablation'] = run_ablation_test()
    
    if args.method == 'all' or args.method == 'smart':
        results['smart'] = run_smart_selector()
    
    if args.method == 'all' or args.method == 'full':
        results['full'] = run_full_selector()
    
    if args.method == 'all' or args.method == 'new':
        results['new'] = run_new_system()
    
    # 显示结果摘要
    print("\n" + "=" * 60)
    print("📊 测试结果摘要")
    print("=" * 60)
    
    for method, success in results.items():
        status = "✅ 成功" if success else "❌ 失败"
        print(f"{method.upper():<12} {status}")
    
    successful_count = sum(results.values())
    total_count = len(results)
    
    print(f"\n📈 总体结果: {successful_count}/{total_count} 成功")
    
    if successful_count == total_count:
        print("🎉 所有测试都成功完成！")
    elif successful_count > 0:
        print("⚠️ 部分测试成功，请检查失败的测试")
    else:
        print("❌ 所有测试都失败，请检查环境配置")
    
    print(f"\n💡 建议:")
    if successful_count < total_count:
        print("• 检查MT5连接配置")
        print("• 确认所有依赖包已安装")
        print("• 查看详细错误信息")
    
    print("• 使用新系统获得最佳体验")
    print("• 查看 FEATURE_SELECTION_GUIDE.md 了解详细用法")

if __name__ == "__main__":
    main()
