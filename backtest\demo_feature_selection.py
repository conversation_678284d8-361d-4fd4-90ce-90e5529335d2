#!/usr/bin/env python3
"""
特征选择系统演示脚本
==================

这个脚本演示了如何使用新的特征选择系统，包括：
1. 基本功能演示
2. 自定义特征添加
3. 不同测试模式的使用
4. 结果分析和可视化

作者: Augment Agent
日期: 2025-09-27
"""

import sys
import os
import numpy as np
import pandas as pd
from datetime import datetime

# 添加路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from feature_selection_controller import FeatureSelectionController
from feature_selection_system import SystemConfig

def demo_feature_library():
    """演示特征库功能"""
    print("🔧 特征库功能演示")
    print("=" * 50)
    
    controller = FeatureSelectionController()
    
    # 显示所有特征
    features_df = controller.get_feature_summary()
    print(f"📊 总特征数: {len(features_df)}")
    
    # 按类别显示
    print("\n📂 特征分类:")
    for category in features_df['category'].unique():
        count = len(features_df[features_df['category'] == category])
        print(f"  • {category}: {count}个特征")
    
    # 显示基准特征
    baseline = controller.feature_library.get_baseline_features()
    print(f"\n🎯 基准特征 (v0核心): {baseline}")
    
    return controller

def demo_custom_feature(controller):
    """演示自定义特征添加"""
    print("\n🎨 自定义特征演示")
    print("=" * 50)
    
    # 添加一个简单的自定义特征
    def price_momentum_ratio(df):
        """价格动量比率 - 短期动量/长期动量"""
        short_momentum = (df['Close'] / df['Close'].shift(5) - 1)
        long_momentum = (df['Close'] / df['Close'].shift(20) - 1)
        return (short_momentum / (long_momentum + 1e-8)).shift(1)
    
    controller.add_custom_feature(
        name="momentum_ratio",
        calculation_func=price_momentum_ratio,
        description="短期动量与长期动量的比率",
        category="momentum"
    )
    
    # 添加另一个自定义特征
    def volatility_trend(df):
        """波动率趋势 - 当前波动率相对历史平均的位置"""
        high_low = df['High'] - df['Low']
        high_close = np.abs(df['High'] - df['Close'].shift(1))
        low_close = np.abs(df['Low'] - df['Close'].shift(1))
        tr = np.maximum(high_low, np.maximum(high_close, low_close))
        
        current_vol = tr.rolling(5).mean()
        avg_vol = tr.rolling(20).mean()
        return ((current_vol - avg_vol) / avg_vol).shift(1)
    
    controller.add_custom_feature(
        name="volatility_trend",
        calculation_func=volatility_trend,
        description="当前波动率相对历史平均的趋势",
        category="volatility"
    )
    
    print("✅ 已添加2个自定义特征:")
    print("  • momentum_ratio: 动量比率")
    print("  • volatility_trend: 波动率趋势")
    
    # 显示更新后的特征库
    updated_features = controller.get_feature_summary()
    print(f"\n📊 更新后总特征数: {len(updated_features)}")

def demo_ablation_test(controller):
    """演示消融测试 (使用模拟数据)"""
    print("\n🔬 消融测试演示 (模拟)")
    print("=" * 50)
    
    # 注意：这里只是演示流程，实际需要MT5连接
    print("📝 消融测试流程:")
    print("1. 获取历史数据")
    print("2. 计算所有特征")
    print("3. 基于v0基准进行单特征测试")
    print("4. 统计分析和显著性检验")
    print("5. 生成报告和可视化")
    
    # 模拟结果展示
    print("\n📊 模拟测试结果:")
    mock_results = [
        {"feature": "rsi", "improvement": "+3.2%", "significant": "✅"},
        {"feature": "volatility", "improvement": "+1.8%", "significant": "❌"},
        {"feature": "momentum_ratio", "improvement": "+4.1%", "significant": "✅"},
        {"feature": "trend_continuation", "improvement": "+2.5%", "significant": "✅"},
        {"feature": "doji_pattern", "improvement": "-0.3%", "significant": "❌"},
    ]
    
    print("| 特征 | 改进幅度 | 显著性 |")
    print("|------|----------|--------|")
    for result in mock_results:
        print(f"| {result['feature']} | {result['improvement']} | {result['significant']} |")
    
    print("\n🏆 推荐特征: momentum_ratio, rsi, trend_continuation")

def demo_smart_selection():
    """演示智能选择 (概念演示)"""
    print("\n🧠 智能特征选择演示")
    print("=" * 50)
    
    print("🔍 贪心搜索策略:")
    print("1. 从基准特征开始")
    print("2. 逐个测试候选特征")
    print("3. 选择最优改进特征")
    print("4. 重复直到无显著改进")
    
    # 模拟搜索过程
    search_steps = [
        {"step": 1, "features": "v0基准", "sharpe": 2.15},
        {"step": 2, "features": "v0 + momentum_ratio", "sharpe": 2.48},
        {"step": 3, "features": "v0 + momentum_ratio + rsi", "sharpe": 2.63},
        {"step": 4, "features": "v0 + momentum_ratio + rsi + trend_continuation", "sharpe": 2.71},
        {"step": 5, "features": "无显著改进", "sharpe": "停止"},
    ]
    
    print("\n📈 搜索过程:")
    for step in search_steps:
        if isinstance(step["sharpe"], float):
            print(f"步骤{step['step']}: {step['features']} → 夏普比率: {step['sharpe']:.2f}")
        else:
            print(f"步骤{step['step']}: {step['features']}")
    
    print("\n🎯 最优组合: v0基准 + momentum_ratio + rsi + trend_continuation")
    print("📊 最终夏普比率: 2.71 (相比基准提升26%)")

def demo_result_analysis():
    """演示结果分析功能"""
    print("\n📊 结果分析演示")
    print("=" * 50)
    
    print("🔬 分析功能包括:")
    print("1. 特征贡献度量化")
    print("2. 统计显著性检验")
    print("3. 特征相关性分析")
    print("4. 冗余特征识别")
    print("5. 效应大小计算")
    print("6. 置信区间估计")
    
    print("\n📈 可视化图表:")
    print("• 特征改进效果条形图")
    print("• 夏普比率对比图")
    print("• 效应大小散点图")
    print("• 特征相关性热力图")
    
    print("\n📄 自动报告生成:")
    print("• Markdown格式分析报告")
    print("• 详细统计结果表格")
    print("• 特征选择建议")
    print("• 风险提示和注意事项")

def demo_integration_workflow():
    """演示与现有工具的集成"""
    print("\n🔗 系统集成演示")
    print("=" * 50)
    
    print("🏗️ 与现有工具的关系:")
    print("• hmm_backtest_v0.py → 基准特征定义")
    print("• feature_ablation_test.py → 消融测试逻辑")
    print("• feature_selector_smart.py → 智能搜索算法")
    print("• feature_selector_full.py → 完整特征库")
    
    print("\n🔄 统一工作流程:")
    print("1. 特征库管理 → 统一特征定义")
    print("2. 标准化回测 → 确保公平比较")
    print("3. 科学分析 → 验证统计显著性")
    print("4. 自动化流程 → 提高效率")
    print("5. 结果可视化 → 便于决策")
    
    print("\n✨ 新系统优势:")
    print("• 避免代码重复")
    print("• 统一评估标准")
    print("• 科学统计分析")
    print("• 自动化程度高")
    print("• 结果可解释性强")

def main():
    """主演示函数"""
    print("🚀 特征选择系统演示")
    print("=" * 60)
    print("这是一个全新的系统化特征选择框架演示")
    print("专为HMM黄金交易策略设计")
    print("=" * 60)
    
    try:
        # 1. 特征库演示
        controller = demo_feature_library()
        
        # 2. 自定义特征演示
        demo_custom_feature(controller)
        
        # 3. 消融测试演示
        demo_ablation_test(controller)
        
        # 4. 智能选择演示
        demo_smart_selection()
        
        # 5. 结果分析演示
        demo_result_analysis()
        
        # 6. 系统集成演示
        demo_integration_workflow()
        
        print("\n" + "=" * 60)
        print("🎉 演示完成！")
        print("=" * 60)
        
        print("\n📚 下一步:")
        print("1. 查看使用指南: cat FEATURE_SELECTION_GUIDE.md")
        print("2. 运行实际测试: python run_feature_selection.py --list-features")
        print("3. 开始消融测试: python run_feature_selection.py --mode ablation")
        print("4. 智能特征选择: python run_feature_selection.py --mode smart")
        
        print("\n💡 提示:")
        print("• 确保MT5连接正常")
        print("• 检查config.json配置")
        print("• 从小规模测试开始")
        print("• 关注统计显著性")
        
    except Exception as e:
        print(f"❌ 演示过程出错: {e}")
        print("💡 这可能是因为缺少MT5连接或依赖包")
        print("   演示主要展示系统架构和使用方法")

if __name__ == "__main__":
    main()
