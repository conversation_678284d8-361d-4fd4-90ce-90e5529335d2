#!/usr/bin/env python3
"""
HMM 7*24小时自动化交易系统
基于现有策略代码，添加完整的自动化交易功能
专业级量化交易系统，确保稳定运行和风险控制
"""

import pandas as pd
import numpy as np
import MetaTrader5 as mt5
from hmmlearn.hmm import GaussianHMM
from sklearn.preprocessing import StandardScaler
from datetime import datetime, timezone, timedelta
import time
import threading
import logging
import json
import os
import sys
from typing import Optional, Dict, Tuple
import traceback
from dataclasses import dataclass, asdict
from enum import Enum
import pytz
from config_loader import load_config

# =============================================================================
# 配置类和常量定义
# =============================================================================

class TradingMode(Enum):
    """交易模式"""
    BACKTEST = "backtest"    # 回测模式
    PAPER = "paper"          # 模拟交易
    LIVE = "live"            # 实盘交易

class SignalType(Enum):
    """信号类型"""
    BUY = 1
    SELL = -1
    HOLD = 0

@dataclass
class TradingConfig:
    """交易配置"""
    # MT5连接配置 - 使用配置文件，不再硬编码
    mt5_path: str = ""  # 从配置文件加载
    login_id: int = 0  # 从配置文件加载
    password: str = ""  # 从配置文件加载
    server_name: str = ""  # 从配置文件加载
    
    # 交易配置
    symbol: str = "XAUUSD"
    timeframe: str = "M1"
    trading_mode: TradingMode = TradingMode.PAPER
    
    # 策略参数
    n_states: int = 3
    ma_fast: int = 20
    ma_slow: int = 35
    lookback_days: int = 20
    
    # 风险管理
    max_position_size: float = 1.0   # 最大仓位（手数）
    risk_per_trade: float = 0.02     # 每笔交易风险比例（2%）
    max_daily_loss: float = 0.05     # 最大日亏损比例（5%）
    max_drawdown: float = 0.10       # 最大回撤比例（10%）
    stop_loss_points: int = 100      # 止损点数
    take_profit_points: int = 200    # 止盈点数
    enable_stop_loss: bool = False   # 是否启用止损
    enable_take_profit: bool = False # 是否启用止盈

    # 系统参数
    reconnect_attempts: int = 5      # 重连尝试次数
    reconnect_delay: int = 30        # 重连延迟（秒）
    heartbeat_interval: int = 60     # 心跳检测间隔（秒）
    data_update_interval: int = 10   # 数据更新间隔（秒）
    
    # 时区配置
    local_timezone: str = 'Asia/Shanghai'
    server_timezone: str = 'Europe/Athens'  # MetaQuotes Demo服务器时区

# =============================================================================
# 日志配置
# =============================================================================

def setup_logging() -> logging.Logger:
    """设置日志系统"""
    # 创建logs目录
    os.makedirs('logs', exist_ok=True)
    
    # 配置日志格式
    log_format = '%(asctime)s | %(levelname)s | %(name)s | %(message)s'
    
    # 设置根日志记录器
    logging.basicConfig(
        level=logging.INFO,
        format=log_format,
        handlers=[
            # 文件日志（按日期分割）
            logging.FileHandler(
                f'logs/hmm_trader_{datetime.now().strftime("%Y%m%d")}.log',
                encoding='utf-8'
            ),
            # 控制台日志
            logging.StreamHandler(sys.stdout)
        ]
    )
    
    logger = logging.getLogger('HMMAutoTrader')
    logger.info("=" * 80)
    logger.info("HMM 7*24小时自动化交易系统启动")
    logger.info("=" * 80)
    
    return logger

# =============================================================================
# MT5连接管理器
# =============================================================================

class MT5ConnectionManager:
    """MT5连接管理器 - 处理连接、重连、心跳检测"""
    
    def __init__(self, config: TradingConfig, logger: logging.Logger):
        self.config = config
        self.logger = logger
        self.connected = False
        self.last_heartbeat = None
        self._lock = threading.Lock()
        
        # 时区设置
        self.local_tz = pytz.timezone(config.local_timezone)
        self.server_tz = pytz.timezone(config.server_timezone)
        self.gmt_offset = None
        
    def connect(self) -> bool:
        """连接到MT5"""
        with self._lock:
            try:
                if not mt5.initialize(
                    path=self.config.mt5_path,
                    login=self.config.login_id,
                    password=self.config.password,
                    server=self.config.server_name,
                    timeout=60000
                ):
                    error_code = mt5.last_error()
                    self.logger.error(f"MT5初始化失败: {error_code}")
                    return False
                
                # 验证品种可用性
                symbol_info = mt5.symbol_info(self.config.symbol)
                if symbol_info is None:
                    self.logger.error(f"品种 {self.config.symbol} 不可用")
                    return False
                
                if not symbol_info.visible:
                    if not mt5.symbol_select(self.config.symbol, True):
                        self.logger.error(f"无法选择品种 {self.config.symbol}")
                        return False
                
                # 计算时区偏移
                self._calculate_gmt_offset()
                
                self.connected = True
                self.last_heartbeat = datetime.now()
                
                account_info = mt5.account_info()
                if account_info:
                    self.logger.info(f"MT5连接成功 - 账户: {account_info.login}, 余额: ${account_info.balance:.2f}")
                else:
                    self.logger.info("MT5连接成功")
                
                return True
                
            except Exception as e:
                self.logger.error(f"MT5连接异常: {e}")
                return False
    
    def disconnect(self):
        """断开MT5连接"""
        with self._lock:
            if self.connected:
                mt5.shutdown()
                self.connected = False
                self.logger.info("MT5连接已断开")
    
    def _calculate_gmt_offset(self):
        """计算服务器时区偏移"""
        try:
            # 获取服务器时间
            tick_info = mt5.symbol_info_tick(self.config.symbol)
            if tick_info:
                server_time = pd.Timestamp(tick_info.time, unit='s')
            else:
                rates = mt5.copy_rates_from_pos(self.config.symbol, mt5.TIMEFRAME_M1, 0, 1)
                if rates:
                    server_time = pd.Timestamp(rates[0]['time'], unit='s')
                else:
                    raise Exception("无法获取服务器时间")
            
            # 计算与UTC的偏移
            utc_time = pd.Timestamp.now(tz='UTC').tz_localize(None)
            self.gmt_offset = round((server_time - utc_time).total_seconds() / 3600)
            
            self.logger.info(f"服务器时区偏移: GMT{self.gmt_offset:+d}")
            
        except Exception as e:
            self.logger.warning(f"时区偏移计算失败: {e}, 使用默认GMT+3")
            self.gmt_offset = 3
    
    def reconnect(self) -> bool:
        """重连逻辑"""
        if self.connected:
            return True
        
        self.logger.info("开始重连MT5...")
        
        for attempt in range(self.config.reconnect_attempts):
            self.logger.info(f"重连尝试 {attempt + 1}/{self.config.reconnect_attempts}")
            
            # 先断开现有连接
            self.disconnect()
            time.sleep(5)
            
            # 尝试重新连接
            if self.connect():
                self.logger.info("重连成功")
                return True
            
            if attempt < self.config.reconnect_attempts - 1:
                self.logger.info(f"重连失败，{self.config.reconnect_delay}秒后重试...")
                time.sleep(self.config.reconnect_delay)
        
        self.logger.error("所有重连尝试失败")
        return False
    
    def heartbeat_check(self) -> bool:
        """心跳检测"""
        try:
            if not self.connected:
                return False
            
            # 尝试获取账户信息作为心跳
            account_info = mt5.account_info()
            if account_info is None:
                self.logger.warning("心跳检测失败: 无法获取账户信息")
                self.connected = False
                return False
            
            self.last_heartbeat = datetime.now()
            return True
            
        except Exception as e:
            self.logger.warning(f"心跳检测异常: {e}")
            self.connected = False
            return False

# =============================================================================
# 数据管理器
# =============================================================================

class DataManager:
    """数据管理器 - 处理历史数据和实时数据"""
    
    def __init__(self, config: TradingConfig, connection_manager: MT5ConnectionManager, logger: logging.Logger):
        self.config = config
        self.connection_manager = connection_manager
        self.logger = logger
        
        self.historical_data = None
        self.features = ['feature_log_return', 'momentum_5m', 'momentum_20m', 'price_position', 'ma_diff']
        
        # 时间框架映射
        self.timeframe_map = {
            "M1": mt5.TIMEFRAME_M1,
            "M5": mt5.TIMEFRAME_M5, 
            "M15": mt5.TIMEFRAME_M15,
            "H1": mt5.TIMEFRAME_H1
        }
    
    def load_historical_data(self) -> bool:
        """加载历史数据"""
        try:
            if not self.connection_manager.connected:
                if not self.connection_manager.reconnect():
                    return False
            
            end_date = datetime.now(timezone.utc)
            start_date = end_date - timedelta(days=self.config.lookback_days)
            
            mt5_timeframe = self.timeframe_map[self.config.timeframe]
            rates = mt5.copy_rates_range(
                self.config.symbol, 
                mt5_timeframe,
                start_date, 
                end_date
            )
            
            if rates is None or len(rates) == 0:
                self.logger.error("历史数据获取失败")
                return False
            
            # 转换为DataFrame
            df = pd.DataFrame(rates)
            df['time'] = pd.to_datetime(df['time'], unit='s')
            
            # 时区转换
            if self.connection_manager.gmt_offset:
                df['time'] = df['time'] - pd.Timedelta(hours=self.connection_manager.gmt_offset)
            df['time'] = df['time'].dt.tz_localize('UTC')
            df.set_index('time', inplace=True)
            
            # 重命名列
            df.rename(columns={
                'open': 'Open', 'high': 'High', 'low': 'Low', 
                'close': 'Close', 'tick_volume': 'Volume'
            }, inplace=True)
            
            # 计算技术特征
            self.historical_data = self._calculate_features(df)
            self.historical_data.dropna(inplace=True)
            
            self.logger.info(f"历史数据加载完成: {len(self.historical_data)} 条记录")
            self.logger.info(f"时间范围: {self.historical_data.index[0].strftime('%Y-%m-%d %H:%M')} 到 {self.historical_data.index[-1].strftime('%Y-%m-%d %H:%M')}")
            
            return True
            
        except Exception as e:
            self.logger.error(f"加载历史数据失败: {e}")
            return False
    
    def _calculate_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """计算技术特征 - 严格遵循t-1时序原则避免前视偏差"""
        df = df.copy()

        # 基础收益率 (用于标签)
        df['log_return'] = np.log(df['Close'] / df['Close'].shift(1))

        # 特征工程 - 所有特征严格滞后1期避免前视偏差
        # 优化为v0最优特征组合（夏普比率8.12 vs 原版4.61）
        df['feature_log_return'] = df['log_return'].shift(1)

        # 动量特征 - 使用线性收益率（在此数据集上效果最优）
        df['momentum_5m'] = (df['Close'] / df['Close'].shift(5) - 1).shift(1)
        df['momentum_20m'] = (df['Close'] / df['Close'].shift(20) - 1).shift(1)

        # 价格位置特征 - 使用Williams %R风格（最优效果）
        df['price_position'] = ((df['Close'] - df['Low'].rolling(20).min()) /
                               (df['High'].rolling(20).max() - df['Low'].rolling(20).min())).shift(1)

        # 移动平均线特征 - 相对差值
        df['ma_fast'] = df['Close'].rolling(window=self.config.ma_fast).mean().shift(1)
        df['ma_slow'] = df['Close'].rolling(window=self.config.ma_slow).mean().shift(1)
        df['ma_diff'] = ((df['ma_fast'] - df['ma_slow']) / df['ma_slow']).shift(1)

        return df
    
    def get_latest_candle(self) -> Optional[Dict]:
        """获取最新完整K线数据"""
        try:
            if not self.connection_manager.connected:
                return None
            
            mt5_timeframe = self.timeframe_map[self.config.timeframe]
            rates = mt5.copy_rates_from_pos(self.config.symbol, mt5_timeframe, 0, 2)
            
            if rates is None or len(rates) < 2:
                return None
            
            # 取倒数第二根K线（完整的）
            candle = rates[-2]
            
            # 时间转换
            utc_time = pd.Timestamp(candle['time'], unit='s')
            if self.connection_manager.gmt_offset:
                utc_time -= pd.Timedelta(hours=self.connection_manager.gmt_offset)
            utc_time = utc_time.tz_localize('UTC')
            
            return {
                'datetime': utc_time,
                'open': float(candle['open']),
                'high': float(candle['high']),
                'low': float(candle['low']),
                'close': float(candle['close']),
                'volume': float(candle['tick_volume'])
            }
            
        except Exception as e:
            self.logger.error(f"获取最新K线失败: {e}")
            return None
    
    def update_historical_data(self, new_candle: Dict) -> bool:
        """更新历史数据"""
        try:
            if self.historical_data is None:
                return False
            
            # 创建新数据行
            new_row = pd.DataFrame([{
                'Open': new_candle['open'],
                'High': new_candle['high'],
                'Low': new_candle['low'],
                'Close': new_candle['close'],
                'Volume': new_candle['volume']
            }], index=[new_candle['datetime']])
            
            # 合并数据
            self.historical_data = pd.concat([self.historical_data, new_row])
            self.historical_data = self.historical_data[~self.historical_data.index.duplicated(keep='last')]
            
            # 重新计算特征
            self.historical_data = self._calculate_features(self.historical_data)
            
            # 保持数据长度（避免内存过大）
            max_length = self.config.lookback_days * 24 * 60  # 假设M1数据
            if len(self.historical_data) > max_length:
                self.historical_data = self.historical_data.tail(max_length)
            
            return True
            
        except Exception as e:
            self.logger.error(f"更新历史数据失败: {e}")
            return False

# =============================================================================
# HMM策略引擎
# =============================================================================

class HMMStrategyEngine:
    """HMM策略引擎 - 基于原有代码的策略实现"""
    
    def __init__(self, config: TradingConfig, data_manager: DataManager, logger: logging.Logger):
        self.config = config
        self.data_manager = data_manager
        self.logger = logger
        
        # HMM模型组件
        self.model: Optional[GaussianHMM] = None
        self.scaler: Optional[StandardScaler] = None
        self.state_map: Dict[int, str] = {}
        self.features = ['feature_log_return', 'momentum_5m', 'momentum_20m', 'price_position', 'ma_diff']
        
        # 策略状态
        self.is_trained = False
        self.last_signal = SignalType.HOLD
        self.best_params = {}
        
        # 🔧 新增：状态持久性跟踪
        self.current_state = None
        self.state_start_time = None
        self.state_duration_minutes = 0
        self.max_state_duration = 120  # 最大状态持续时间（分钟）
        
    def walk_forward_analysis(self) -> bool:
        """前向展开分析优化"""
        try:
            self.logger.info("开始前向展开分析...")
            
            df = self.data_manager.historical_data
            if df is None or len(df) < 1000:
                self.logger.error("数据不足，无法进行前向分析")
                return False
            
            results = []
            train_ratios = np.arange(0.6, 0.85, 0.05)
            
            for train_ratio in train_ratios:
                try:
                    split_idx = int(len(df) * train_ratio)
                    train_data = df.iloc[:split_idx]
                    test_data = df.iloc[split_idx:]
                    
                    if len(test_data) < 100:
                        continue
                    
                    # 训练模型
                    X_train = train_data[self.features].dropna().values
                    if len(X_train) < 100:
                        continue
                        
                    scaler = StandardScaler().fit(X_train)
                    X_train_scaled = scaler.transform(X_train)
                    
                    model = GaussianHMM(
                        n_components=self.config.n_states,
                        covariance_type="diag",
                        n_iter=300,
                        random_state=42,
                        tol=1e-4
                    ).fit(X_train_scaled)
                    
                    # 状态映射 - 使用feature_log_return
                    state_means = pd.DataFrame(model.means_, columns=self.features)
                    sorted_by_return = state_means.sort_values('feature_log_return')
                    
                    temp_state_map = {
                        sorted_by_return.index[0]: "下跌",
                        sorted_by_return.index[-1]: "上涨"
                    }
                    if self.config.n_states == 3:
                        temp_state_map[sorted_by_return.index[1]] = "盘整"
                    
                    # 回测 - 需要包含log_return列用于收益计算
                    test_features = test_data[self.features + ['log_return']].dropna()
                    if len(test_features) < 50:
                        continue

                    X_test_scaled = scaler.transform(test_features[self.features].values)
                    test_states = model.predict(X_test_scaled)

                    # 信号生成（使用shift避免未来信息泄露）
                    test_signals = pd.Series(test_states).map({
                        k: 1 if v == "上涨" else (-1 if v == "下跌" else 0)
                        for k, v in temp_state_map.items()
                    }).shift(1).fillna(0).values

                    # 收益计算
                    test_returns = test_features['log_return'].values
                    strategy_returns = test_returns * test_signals[:len(test_returns)]
                    
                    # 性能指标 - 基于实际交易频率的科学年化
                    if len(strategy_returns) > 0 and np.std(strategy_returns) > 0:
                        # 计算信号变化频率以确定实际交易频率
                        signal_changes = np.diff(test_signals) != 0
                        if np.sum(signal_changes) > 0:
                            avg_holding_periods = len(test_signals) / np.sum(signal_changes)
                            minutes_per_year = 252 * 23 * 60  # 347,760
                            trades_per_year = minutes_per_year / avg_holding_periods
                            sharpe = np.mean(strategy_returns) / np.std(strategy_returns) * np.sqrt(trades_per_year)
                        else:
                            # 如果没有信号变化，使用保守的年化系数
                            sharpe = np.mean(strategy_returns) / np.std(strategy_returns) * np.sqrt(252)
                        max_dd = self._calculate_max_drawdown(strategy_returns)
                        total_return = np.exp(np.sum(strategy_returns)) - 1
                        
                        results.append({
                            'train_ratio': train_ratio,
                            'sharpe_ratio': sharpe,
                            'max_drawdown': max_dd,
                            'total_return': total_return
                        })
                    
                except Exception as e:
                    self.logger.warning(f"训练比例 {train_ratio:.2f} 分析失败: {e}")
                    continue
            
            if not results:
                self.logger.error("前向分析失败，使用默认参数")
                self.best_params = {'train_ratio': 0.75}
                return False
            
            # 选择最优参数
            results_df = pd.DataFrame(results)
            results_df['score'] = results_df['sharpe_ratio'] * 0.7 - results_df['max_drawdown'] * 0.3
            best_result = results_df.loc[results_df['score'].idxmax()]
            
            self.best_params = best_result.to_dict()
            
            self.logger.info(f"前向分析完成 - 最佳训练比例: {best_result['train_ratio']:.2f}")
            self.logger.info(f"样本外夏普比率: {best_result['sharpe_ratio']:.2f}")
            self.logger.info(f"样本外最大回撤: {best_result['max_drawdown']:.2%}")
            
            return True
            
        except Exception as e:
            self.logger.error(f"前向展开分析失败: {e}")
            return False
    
    def _calculate_max_drawdown(self, returns: np.ndarray) -> float:
        """计算最大回撤"""
        cumulative = np.exp(np.cumsum(returns))
        running_max = np.maximum.accumulate(cumulative)
        drawdown = (cumulative - running_max) / running_max
        return abs(drawdown.min())
    
    def train_model(self) -> bool:
        """训练HMM模型"""
        try:
            if self.data_manager.historical_data is None:
                self.logger.error("历史数据未加载")
                return False
            
            # 使用最优训练比例
            train_ratio = self.best_params.get('train_ratio', 0.75)
            df = self.data_manager.historical_data
            split_idx = int(len(df) * train_ratio)
            train_data = df.iloc[:split_idx]
            
            # 准备训练数据
            X_train = train_data[self.features].dropna().values
            if len(X_train) < 100:
                self.logger.error("训练数据不足")
                return False
            
            # 特征标准化
            self.scaler = StandardScaler()
            X_train_scaled = self.scaler.fit_transform(X_train)
            
            # 训练HMM模型
            self.model = GaussianHMM(
                n_components=self.config.n_states,
                covariance_type="diag",
                n_iter=500,
                random_state=42,
                tol=1e-4
            )
            self.model.fit(X_train_scaled)
            
            # 状态映射
            self._identify_state_mapping()
            
            self.is_trained = True
            
            self.logger.info(f"HMM模型训练完成 - 使用 {len(X_train)} 条训练数据")
            self.logger.info(f"BIC得分: {self.model.bic(X_train_scaled):.2f}")
            
            return True
            
        except Exception as e:
            self.logger.error(f"模型训练失败: {e}")
            return False
    
    def _identify_state_mapping(self):
        """识别状态映射 - 基于feature_log_return避免前视偏差"""
        state_means = pd.DataFrame(self.model.means_, columns=self.features)
        sorted_by_return = state_means.sort_values('feature_log_return')
        
        self.state_map = {
            sorted_by_return.index[0]: "下跌",
            sorted_by_return.index[-1]: "上涨"
        }
        if self.config.n_states == 3:
            self.state_map[sorted_by_return.index[1]] = "盘整"
        
        self.logger.info("状态映射:")
        for state, regime in self.state_map.items():
            ret = state_means.loc[state, 'feature_log_return']
            ma_diff = state_means.loc[state, 'ma_diff']
            self.logger.info(f"  状态{state} -> {regime} (滞后收益:{ret:.4f}, 均线差:{ma_diff:.4f})")
        
        # 🔧 新增：分析状态转移矩阵和持久性
        self._analyze_state_transitions()
    
    def _analyze_state_transitions(self):
        """🔧 分析状态转移矩阵和持久性 - 诊断状态卡住问题"""
        if self.model is None:
            return
        
        self.logger.info("\n🔍 状态转移矩阵分析:")
        transition_df = pd.DataFrame(
            self.model.transmat_, 
            columns=[f'到{self.state_map.get(i, f"状态{i}")}' for i in range(self.config.n_states)], 
            index=[f'从{self.state_map.get(i, f"状态{i}")}' for i in range(self.config.n_states)]
        )
        
        # 记录转移矩阵（格式化输出）
        for i, row in enumerate(transition_df.values):
            from_state = f'从{self.state_map.get(i, f"状态{i}")}'
            transitions = ' '.join([f'{val:.3f}' for val in row])
            self.logger.info(f"  {from_state}: [{transitions}]")
        
        # 计算期望状态持续时间
        expected_durations = 1 / (1 - np.diag(self.model.transmat_))
        self.logger.info("\n⏰ 期望状态持续时间:")
        max_duration = 0
        problematic_states = []
        
        for i, duration in enumerate(expected_durations):
            regime = self.state_map.get(i, f"状态{i}")
            self_transition_prob = self.model.transmat_[i, i]
            self.logger.info(f"  {regime}: {duration:.1f}分钟 (自转移概率: {self_transition_prob:.3f})")
            
            # 检测问题状态（持续时间过长）
            if duration > 100:  # 超过100分钟认为过长
                problematic_states.append((regime, duration, self_transition_prob))
                max_duration = max(max_duration, duration)
        
        # 警告信息
        if problematic_states:
            self.logger.warning(f"\n⚠️  状态持久性警告:")
            for regime, duration, prob in problematic_states:
                self.logger.warning(f"  ❌ {regime}状态可能卡住: 期望持续{duration:.1f}分钟 (自转移概率{prob:.3f})")
            self.logger.warning(f"  💡 建议: 如果实时运行中状态不变，这可能是原因")
        else:
            self.logger.info(f"\n✅ 状态转移概率合理，期望最长持续{max_duration:.1f}分钟")
    
    def generate_signal(self, new_candle: Dict) -> Tuple[SignalType, str]:
        """生成交易信号 - 与原始策略完全一致的逻辑"""
        try:
            if not self.is_trained:
                return SignalType.HOLD, "模型未训练"
            
            # 1. 更新历史数据
            if not self.data_manager.update_historical_data(new_candle):
                return SignalType.HOLD, "数据更新失败"
            
            # 2. 获取有效数据
            df = self.data_manager.historical_data
            if len(df) < self.config.ma_slow + 1:
                return SignalType.HOLD, "数据不足"
            
            # 3. 🎯 关键：使用与原始策略完全一致的逻辑
            # 获取所有有效数据进行HMM预测，就像原始回测一样
            valid_data = df.dropna(subset=self.features)
            if len(valid_data) < 2:
                return SignalType.HOLD, "有效数据不足"
            
            # 使用所有可用数据进行HMM预测
            X_all_scaled = self.scaler.transform(valid_data[self.features].values)
            all_states = self.model.predict(X_all_scaled)
            
            # 创建临时DataFrame，使用与回测完全相同的逻辑
            temp_df = valid_data.copy()
            temp_df['state'] = all_states
            temp_df['regime'] = temp_df['state'].map(self.state_map)
            # 关键：使用shift(1)避免未来信息泄露，与原始策略一致
            temp_df['signal'] = temp_df['regime'].map({"上涨": 1, "下跌": -1, "盘整": 0}).shift(1).fillna(0)
            
            # 获取最新K线的状态和信号
            latest_row = temp_df.iloc[-1]
            current_regime = latest_row['regime']
            current_signal = int(latest_row['signal'])
            current_state = latest_row['state']
            
            # 🔧 新增：状态持续时间跟踪和警告
            current_time = new_candle['datetime']
            
            # 检查状态是否发生变化
            if self.current_state != current_state:
                if self.current_state is not None:
                    # 记录上一个状态的持续时间
                    old_regime = self.state_map.get(self.current_state, f"状态{self.current_state}")
                    if self.state_start_time:
                        duration = (current_time - self.state_start_time).total_seconds() / 60
                        self.logger.info(f"📊 状态变化: {old_regime} -> {current_regime} (持续了{duration:.1f}分钟)")
                
                # 更新状态跟踪信息
                self.current_state = current_state
                self.state_start_time = current_time
                self.state_duration_minutes = 0
            else:
                # 计算当前状态持续时间
                if self.state_start_time:
                    self.state_duration_minutes = (current_time - self.state_start_time).total_seconds() / 60
                    
                    # 🚨 状态持续时间警告
                    if self.state_duration_minutes > self.max_state_duration:
                        self.logger.warning(f"⚠️  状态持久性警告: {current_regime}状态已持续{self.state_duration_minutes:.1f}分钟")
                        self.logger.warning(f"  💡 可能原因: HMM模型在当前市场环境下状态转移概率过高")
                        # 重置计时器，避免频繁警告
                        self.state_start_time = current_time
            
            # 转换信号格式
            if current_signal == 1:
                signal = SignalType.BUY
            elif current_signal == -1:
                signal = SignalType.SELL
            else:
                signal = SignalType.HOLD
            
            # 构建状态信息字符串
            state_info = f"当前状态: {current_regime}"
            if self.state_duration_minutes > 0:
                state_info += f" (持续{self.state_duration_minutes:.0f}分钟)"
            
            return signal, state_info
            
        except Exception as e:
            self.logger.error(f"信号生成失败: {e}")
            return SignalType.HOLD, f"信号生成异常: {e}"

# =============================================================================
# 风险管理器
# =============================================================================

class RiskManager:
    """风险管理器 - 处理仓位大小、止损止盈、风险控制"""
    
    def __init__(self, config: TradingConfig, connection_manager: MT5ConnectionManager, logger: logging.Logger):
        self.config = config
        self.connection_manager = connection_manager
        self.logger = logger
        
        # 风险统计
        self.daily_pnl = 0.0
        self.max_drawdown_current = 0.0
        self.peak_equity = 0.0
        self.last_reset_date = datetime.now().date()
    
    def can_trade(self) -> Tuple[bool, str]:
        """检查是否可以交易"""
        try:
            # 检查连接状态
            if not self.connection_manager.connected:
                return False, "MT5未连接"
            
            # 获取账户信息
            account_info = mt5.account_info()
            if account_info is None:
                return False, "无法获取账户信息"
            
            # 重置日统计（如果是新的一天）
            current_date = datetime.now().date()
            if current_date != self.last_reset_date:
                self.daily_pnl = 0.0
                self.daily_start_equity = account_info.equity  # 重置日起始权益
                self.last_reset_date = current_date
                self.logger.info(f"日统计已重置 - 日起始权益: ${account_info.equity:.2f}")
            
            # 更新回撤统计
            current_equity = account_info.equity
            if current_equity > self.peak_equity:
                self.peak_equity = current_equity
            
            if self.peak_equity > 0:
                self.max_drawdown_current = (self.peak_equity - current_equity) / self.peak_equity
            
            # 检查最大回撤限制
            if self.max_drawdown_current >= self.config.max_drawdown:
                return False, f"达到最大回撤限制 ({self.max_drawdown_current:.2%})"
            
            # 检查日亏损限制 - 使用当日累计盈亏
            # 简化版：使用当前权益与初始权益的差值作为日盈亏
            if not hasattr(self, 'daily_start_equity'):
                self.daily_start_equity = account_info.equity
                
            current_daily_pnl = account_info.equity - self.daily_start_equity
            daily_loss_percent = abs(current_daily_pnl) / account_info.balance if account_info.balance > 0 else 0
            
            if current_daily_pnl < 0 and daily_loss_percent >= self.config.max_daily_loss:
                return False, f"达到日亏损限制 ({daily_loss_percent:.2%}, 亏损: ${current_daily_pnl:.2f})"
            
            # 检查保证金水平
            if account_info.margin_level < 200 and account_info.margin > 0:
                return False, f"保证金水平过低 ({account_info.margin_level:.1f}%)"
            
            return True, "风险检查通过"
            
        except Exception as e:
            self.logger.error(f"风险检查失败: {e}")
            return False, f"风险检查异常: {e}"
    
    def calculate_position_size(self, signal_type: SignalType) -> float:
        """计算合适的仓位大小"""
        try:
            if signal_type == SignalType.HOLD:
                return 0.0
            
            account_info = mt5.account_info()
            if account_info is None:
                return 0.0
            
            # 基础风险金额
            risk_amount = account_info.balance * self.config.risk_per_trade
            
            # 获取品种信息
            symbol_info = mt5.symbol_info(self.config.symbol)
            if symbol_info is None:
                return 0.0
            
            # 计算每手风险
            point_value = symbol_info.trade_contract_size * symbol_info.point
            risk_per_lot = self.config.stop_loss_points * point_value
            
            # 计算仓位大小
            position_size = risk_amount / risk_per_lot if risk_per_lot > 0 else 0
            
            # 限制到最大仓位
            position_size = min(position_size, self.config.max_position_size)
            
            # 调整到合法步长
            min_lot = symbol_info.volume_min
            lot_step = symbol_info.volume_step
            position_size = max(min_lot, round(position_size / lot_step) * lot_step)
            
            self.logger.info(f"计算仓位大小: {position_size:.3f} 手 (风险金额: ${risk_amount:.2f})")
            
            return position_size
            
        except Exception as e:
            self.logger.error(f"仓位计算失败: {e}")
            return 0.0

# =============================================================================
# 交易执行器
# =============================================================================

class TradeExecutor:
    """交易执行器 - 处理订单执行、持仓管理"""
    
    def __init__(self, config: TradingConfig, connection_manager: MT5ConnectionManager, risk_manager: RiskManager, logger: logging.Logger):
        self.config = config
        self.connection_manager = connection_manager
        self.risk_manager = risk_manager
        self.logger = logger
        
        self.magic_number = 20241203  # 唯一标识
        self.current_position = None
    
    def execute_signal(self, signal: SignalType, reason: str) -> bool:
        """执行交易信号"""
        try:
            # 风险检查
            can_trade, risk_msg = self.risk_manager.can_trade()
            if not can_trade:
                self.logger.warning(f"交易被风险管理器阻止: {risk_msg}")
                return False
            
            # 获取当前持仓
            current_positions = self._get_positions()
            
            # 根据信号类型执行操作
            if signal == SignalType.HOLD:
                # 🔧 修复：HOLD信号表示空仓，应该平掉所有持仓
                # 这与Reference版本的逻辑一致：空仓信号 = 平仓/空仓
                if current_positions:
                    self.logger.info(f"HOLD信号(空仓信号) - 平掉当前持仓: {current_positions['type']}")
                    return self._close_position(current_positions, "空仓信号平仓")
                else:
                    self.logger.info("HOLD信号(空仓信号) - 当前已无持仓")
                    return True
            
            elif signal == SignalType.BUY:
                # 🔧 做多信号：确保只持有多头仓位（一次一单原则）
                if current_positions:
                    if current_positions['type'] == 'SELL':
                        # 有空头持仓，先平仓再开多仓
                        self.logger.info("BUY信号 - 平掉空头仓位，准备开多仓")
                        if not self._close_position(current_positions, "BUY信号平空仓"):
                            return False
                        # 平仓后开多仓
                        return self._open_position('BUY', reason)
                    elif current_positions['type'] == 'BUY':
                        # 已有多头持仓，保持不变
                        self.logger.info("BUY信号 - 已有多头持仓，保持当前仓位")
                        return True
                else:
                    # 无持仓，开多仓
                    return self._open_position('BUY', reason)
            
            elif signal == SignalType.SELL:
                # 🔧 做空信号：确保只持有空头仓位（一次一单原则）
                if current_positions:
                    if current_positions['type'] == 'BUY':
                        # 有多头持仓，先平仓再开空仓
                        self.logger.info("SELL信号 - 平掉多头仓位，准备开空仓")
                        if not self._close_position(current_positions, "SELL信号平多仓"):
                            return False
                        # 平仓后开空仓
                        return self._open_position('SELL', reason)
                    elif current_positions['type'] == 'SELL':
                        # 已有空头持仓，保持不变
                        self.logger.info("SELL信号 - 已有空头持仓，保持当前仓位")
                        return True
                else:
                    # 无持仓，开空仓
                    return self._open_position('SELL', reason)
            
            return True
            
        except Exception as e:
            self.logger.error(f"信号执行失败: {e}")
            return False
    
    def _get_positions(self) -> Optional[Dict]:
        """获取当前持仓"""
        try:
            positions = mt5.positions_get(symbol=self.config.symbol)
            if positions and len(positions) > 0:
                # 假设只有一个持仓
                pos = positions[0]
                return {
                    'ticket': pos.ticket,
                    'type': 'BUY' if pos.type == mt5.ORDER_TYPE_BUY else 'SELL',
                    'volume': pos.volume,
                    'price': pos.price_open,
                    'profit': pos.profit,
                    'sl': pos.sl,
                    'tp': pos.tp
                }
            return None
            
        except Exception as e:
            self.logger.error(f"获取持仓失败: {e}")
            return None
    
    def _open_position(self, direction: str, reason: str) -> bool:
        """开仓"""
        try:
            if self.config.trading_mode == TradingMode.BACKTEST:
                self.logger.info(f"[回测模式] 开仓信号: {direction} - {reason}")
                return True
            
            # 计算仓位大小
            signal_type = SignalType.BUY if direction == 'BUY' else SignalType.SELL
            volume = self.risk_manager.calculate_position_size(signal_type)
            
            if volume <= 0:
                self.logger.warning("仓位大小为0，跳过开仓")
                return False
            
            # 获取当前报价
            tick = mt5.symbol_info_tick(self.config.symbol)
            if tick is None:
                self.logger.error("无法获取实时报价")
                return False
            
            # 确定订单参数
            if direction == 'BUY':
                order_type = mt5.ORDER_TYPE_BUY
                price = tick.ask
            else:
                order_type = mt5.ORDER_TYPE_SELL
                price = tick.bid
            
            # 构建订单请求
            request = {
                "action": mt5.TRADE_ACTION_DEAL,
                "symbol": self.config.symbol,
                "volume": volume,
                "type": order_type,
                "price": price,
                "magic": self.magic_number,
                "comment": f"HMM-{direction}-{reason[:20]}",
                "type_time": mt5.ORDER_TIME_GTC,
                "type_filling": mt5.ORDER_FILLING_IOC,
            }
            
            # 🔧 可选的止盈止损设置
            if self.config.enable_stop_loss:
                if direction == 'BUY':
                    sl_price = price - self.config.stop_loss_points * mt5.symbol_info(self.config.symbol).point
                else:
                    sl_price = price + self.config.stop_loss_points * mt5.symbol_info(self.config.symbol).point
                request["sl"] = sl_price
                
            if self.config.enable_take_profit:
                if direction == 'BUY':
                    tp_price = price + self.config.take_profit_points * mt5.symbol_info(self.config.symbol).point
                else:
                    tp_price = price - self.config.take_profit_points * mt5.symbol_info(self.config.symbol).point
                request["tp"] = tp_price
            
            # 发送订单
            if self.config.trading_mode == TradingMode.PAPER:
                self.logger.info(f"[模拟交易] 开仓: {direction} {volume}手 @ {price:.2f} - {reason}")
                return True
            
            # 实盘交易
            result = mt5.order_send(request)
            
            if result.retcode != mt5.TRADE_RETCODE_DONE:
                self.logger.error(f"开仓失败: {result.comment} (代码: {result.retcode})")
                return False
            
            self.logger.info(f"开仓成功: {direction} {volume}手 @ {price:.2f}, 订单号: {result.order}")
            
            # 🔧 只在启用时显示止损止盈信息
            sl_info = ""
            tp_info = ""
            if self.config.enable_stop_loss:
                if direction == 'BUY':
                    sl_price = price - self.config.stop_loss_points * mt5.symbol_info(self.config.symbol).point
                else:
                    sl_price = price + self.config.stop_loss_points * mt5.symbol_info(self.config.symbol).point
                sl_info = f"止损: {sl_price:.2f}"
                
            if self.config.enable_take_profit:
                if direction == 'BUY':
                    tp_price = price + self.config.take_profit_points * mt5.symbol_info(self.config.symbol).point
                else:
                    tp_price = price - self.config.take_profit_points * mt5.symbol_info(self.config.symbol).point
                tp_info = f"止盈: {tp_price:.2f}"
            
            if sl_info or tp_info:
                self.logger.info(f"{sl_info}{', ' if sl_info and tp_info else ''}{tp_info}")
            else:
                self.logger.info("未设置止损止盈 - 将通过HMM信号管理持仓")
                
            self.logger.info(f"原因: {reason}")
            
            return True
            
        except Exception as e:
            self.logger.error(f"开仓异常: {e}")
            return False
    
    def _close_position(self, position: Dict, reason: str = "手动平仓") -> bool:
        """平仓"""
        try:
            if self.config.trading_mode == TradingMode.BACKTEST:
                self.logger.info(f"[回测模式] 平仓: {position['type']} {position['volume']}手 - {reason}")
                return True
            
            # 获取当前报价
            tick = mt5.symbol_info_tick(self.config.symbol)
            if tick is None:
                self.logger.error("无法获取实时报价")
                return False
            
            # 确定平仓参数
            if position['type'] == 'BUY':
                order_type = mt5.ORDER_TYPE_SELL
                price = tick.bid
            else:
                order_type = mt5.ORDER_TYPE_BUY
                price = tick.ask
            
            # 构建平仓请求
            request = {
                "action": mt5.TRADE_ACTION_DEAL,
                "symbol": self.config.symbol,
                "volume": position['volume'],
                "type": order_type,
                "position": position['ticket'],
                "price": price,
                "magic": self.magic_number,
                "comment": "HMM-Close",
                "type_time": mt5.ORDER_TIME_GTC,
                "type_filling": mt5.ORDER_FILLING_IOC,
            }
            
            if self.config.trading_mode == TradingMode.PAPER:
                self.logger.info(f"[模拟交易] 平仓: {position['type']} {position['volume']}手 @ {price:.2f} - {reason}")
                return True
            
            # 执行平仓
            result = mt5.order_send(request)
            
            if result.retcode != mt5.TRADE_RETCODE_DONE:
                self.logger.error(f"平仓失败: {result.comment} (代码: {result.retcode})")
                return False
            
            self.logger.info(f"平仓成功: {position['type']} {position['volume']}手 @ {price:.2f} - {reason}")
            self.logger.info(f"盈亏: ${position['profit']:.2f}")
            
            return True
            
        except Exception as e:
            self.logger.error(f"平仓异常: {e}")
            return False

# =============================================================================
# 主系统控制器
# =============================================================================

class HMMAutoTradingSystem:
    """HMM自动化交易系统主控制器"""
    
    def __init__(self, config: TradingConfig):
        self.config = config
        self.logger = setup_logging()
        
        # 系统组件
        self.connection_manager = MT5ConnectionManager(config, self.logger)
        self.data_manager = DataManager(config, self.connection_manager, self.logger)
        self.strategy_engine = HMMStrategyEngine(config, self.data_manager, self.logger)
        self.risk_manager = RiskManager(config, self.connection_manager, self.logger)
        self.trade_executor = TradeExecutor(config, self.connection_manager, self.risk_manager, self.logger)
        
        # 系统状态
        self.running = False
        self.last_candle_time = None
        self.heartbeat_thread = None
        self.main_thread = None
        
    def initialize(self) -> bool:
        """系统初始化"""
        try:
            self.logger.info("系统初始化开始...")
            
            # 1. 连接MT5
            if not self.connection_manager.connect():
                return False
            
            # 2. 加载历史数据
            if not self.data_manager.load_historical_data():
                return False
            
            # 3. 策略优化
            if not self.strategy_engine.walk_forward_analysis():
                self.logger.warning("策略优化失败，使用默认参数")
            
            # 4. 训练模型
            if not self.strategy_engine.train_model():
                return False
            
            self.logger.info("系统初始化完成")
            return True
            
        except Exception as e:
            self.logger.error(f"系统初始化失败: {e}")
            return False
    
    def start(self):
        """启动系统"""
        try:
            if not self.initialize():
                self.logger.error("系统初始化失败")
                return
            
            self.running = True
            
            # 启动心跳检测线程
            self.heartbeat_thread = threading.Thread(target=self._heartbeat_loop, daemon=True)
            self.heartbeat_thread.start()
            
            # 启动主交易线程
            self.main_thread = threading.Thread(target=self._trading_loop, daemon=True)
            self.main_thread.start()
            
            self.logger.info("=" * 60)
            self.logger.info("HMM自动化交易系统已启动")
            self.logger.info(f"交易模式: {self.config.trading_mode.value}")
            self.logger.info(f"交易品种: {self.config.symbol}")
            self.logger.info(f"时间周期: {self.config.timeframe}")
            self.logger.info("按 Ctrl+C 停止系统")
            self.logger.info("=" * 60)
            
            # 保持主线程运行
            try:
                while self.running:
                    time.sleep(1)
            except KeyboardInterrupt:
                self.logger.info("收到停止信号")
                self.stop()
            
        except Exception as e:
            self.logger.error(f"系统启动失败: {e}")
    
    def stop(self):
        """停止系统"""
        self.logger.info("正在停止系统...")
        self.running = False
        
        # 等待线程结束
        if self.heartbeat_thread and self.heartbeat_thread.is_alive():
            self.heartbeat_thread.join(timeout=5)
        
        if self.main_thread and self.main_thread.is_alive():
            self.main_thread.join(timeout=10)
        
        # 断开连接
        self.connection_manager.disconnect()
        
        self.logger.info("系统已停止")
    
    def _heartbeat_loop(self):
        """心跳检测循环"""
        while self.running:
            try:
                if not self.connection_manager.heartbeat_check():
                    self.logger.warning("心跳检测失败，尝试重连...")
                    if not self.connection_manager.reconnect():
                        self.logger.error("重连失败，系统将在30秒后重试...")
                        time.sleep(30)
                        continue
                
                time.sleep(self.config.heartbeat_interval)
                
            except Exception as e:
                self.logger.error(f"心跳检测异常: {e}")
                time.sleep(30)
    
    def _trading_loop(self):
        """主交易循环"""
        while self.running:
            try:
                # 检查连接状态
                if not self.connection_manager.connected:
                    time.sleep(10)
                    continue
                
                # 获取最新K线
                latest_candle = self.data_manager.get_latest_candle()
                if latest_candle is None:
                    time.sleep(self.config.data_update_interval)
                    continue
                
                # 检查是否是新K线
                if (self.last_candle_time is None or 
                    latest_candle['datetime'] > self.last_candle_time):
                    
                    self.last_candle_time = latest_candle['datetime']
                    
                    # 生成交易信号
                    signal, reason = self.strategy_engine.generate_signal(latest_candle)
                    
                    # 记录K线信息
                    self.logger.info(f"新K线: {latest_candle['datetime'].strftime('%Y-%m-%d %H:%M:%S')} "
                                   f"OHLC: {latest_candle['open']:.2f}/{latest_candle['high']:.2f}/"
                                   f"{latest_candle['low']:.2f}/{latest_candle['close']:.2f}")
                    
                    # 只在信号变化时记录和执行
                    if signal != self.strategy_engine.last_signal:
                        self.logger.info(f"信号变化: {self.strategy_engine.last_signal.name} -> {signal.name}")
                        self.logger.info(f"信号原因: {reason}")
                        
                        # 执行交易
                        execution_result = self.trade_executor.execute_signal(signal, reason)
                        
                        # 🔧 修复：无论执行成功与否，都要更新信号状态，避免重复尝试相同信号
                        # 这确保了系统状态的一致性，防止因执行失败而导致的信号状态混乱
                        self.strategy_engine.last_signal = signal
                        
                        if execution_result:
                            self.logger.info(f"✅ 信号执行成功: {signal.name}")
                        else:
                            self.logger.warning(f"❌ 交易执行失败: {signal.name} (信号状态已更新)")
                    else:
                        # 信号未变化，只记录当前状态（调试用）
                        pass  # 可以选择性地记录状态信息
                
                time.sleep(self.config.data_update_interval)
                
            except Exception as e:
                self.logger.error(f"交易循环异常: {e}")
                self.logger.error(traceback.format_exc())
                time.sleep(30)

# =============================================================================
# 主程序入口
# =============================================================================

def main():
    """主程序入口"""
    print("🚀 HMM 7*24小时自动化交易系统")
    print("=" * 60)

    try:
        # 从配置文件加载配置 - 专业方式，不使用硬编码
        config = load_config("config.json")

        print(f"✅ 已加载配置文件")
        print(f"📊 MT5账户: {config.login_id}")
        print(f"🌐 服务器: {config.server_name}")
        print(f"💱 交易品种: {config.symbol}")
        print(f"📈 交易模式: {config.trading_mode.value}")
        
        # 创建并启动交易系统
        trading_system = HMMAutoTradingSystem(config)
        trading_system.start()
        
    except Exception as e:
        print(f"❌ 系统异常: {e}")
        traceback.print_exc()
    
    finally:
        print("👋 系统退出")

if __name__ == "__main__":
    main()