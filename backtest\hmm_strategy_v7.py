#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
HMM Strategy v7: 统一最优版本  
============================

基于v5+v6成功经验的稳健改进版本
目标: 在保持稳定的基础上超越v6的13.46夏普比率

科学演进逻辑:
- 使用v6验证的最优MA(20,35)参数
- 采用v5的核心特征集 (经过验证)
- 改进状态映射逻辑 (避免过拟合)
- 保持简洁性 (防止过度复杂化)
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from hmmlearn.hmm import GaussianHMM
from sklearn.preprocessing import StandardScaler
import os
import warnings

warnings.filterwarnings('ignore')

# 中文字体配置
plt.rcParams['font.sans-serif'] = ['Arial Unicode MS', 'SimHei', 'Heiti TC', 'PingFang SC', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

def load_and_prepare_data_v7(file_path: str) -> tuple:
    """v7数据准备: 使用v6最优MA参数 + v5核心特征 (已修正前视偏差)"""
    
    if not os.path.exists(file_path):
        raise FileNotFoundError(f"数据文件未找到: {file_path}")
    
    df = pd.read_parquet(file_path)
    if isinstance(df.columns, pd.MultiIndex):
        df.columns = df.columns.get_level_values(0)
    
    print("数据加载成功")
    print(f"原始数据形状: {df.shape}")
    
    # v7特征工程: 精选v5+v6的最优特征
    df_features = df.copy()
    
    # 基础收益特征 (用于标签，不加shift)
    df_features['log_return'] = np.log(df_features['Close'] / df_features['Close'].shift(1))
    
    # 特征工程 (所有特征严格滞后1期，避免前视偏差)
    df_features['feature_log_return'] = df_features['log_return'].shift(1)
    df_features['sma_fast'] = df_features['Close'].rolling(window=20).mean().shift(1)
    df_features['sma_slow'] = df_features['Close'].rolling(window=35).mean().shift(1)
    df_features['ma_diff'] = ((df_features['sma_fast'] - df_features['sma_slow']) / df_features['sma_slow']).shift(1)
    df_features['price_position'] = ((df_features['Close'] - df_features['sma_slow']) / df_features['sma_slow']).shift(1)
    df_features['momentum_5m'] = np.log(df_features['Close'] / df_features['Close'].shift(5)).shift(1)
    df_features['momentum_20m'] = np.log(df_features['Close'] / df_features['Close'].shift(20)).shift(1)
    
    # v7核心特征列表 (5个精选特征)
    features_list = [
        'feature_log_return', # 基础收益 (滞后)
        'ma_diff',            # v6最优MA差值 (滞后)
        'price_position',     # 价格位置 (滞后)
        'momentum_5m',        # 短期动量 (滞后)
        'momentum_20m'        # 中期动量 (滞后)
    ]
    
    df_features.dropna(inplace=True)
    
    print(f"v7精选特征: {features_list}")  
    print(f"特征工程后数据形状: {df_features.shape}")
    
    return df_features, features_list

def train_hmm_v7(train_data: pd.DataFrame, features_list: list) -> tuple:
    """v7 HMM训练: 采用v6验证的稳健参数 (已修正状态映射前视偏差)"""
    
    print(f"\n--- v7 HMM模型训练 ---")
    
    scaler = StandardScaler()
    X_train = scaler.fit_transform(train_data[features_list])
    
    # v6验证的最优参数
    model = GaussianHMM(
        n_components=3,
        covariance_type="diag", 
        n_iter=800,
        random_state=42,
        tol=1e-5
    )
    
    model.fit(X_train)
    bic = model.bic(X_train)
    print(f"模型训练完成，BIC={bic:.2f}")
    
    # 状态映射 (无前视偏差): 基于模型内在均值 (mean) 进行映射
    # 找到 'feature_log_return' 在特征列表中的索引
    try:
        log_return_idx = features_list.index('feature_log_return')
    except ValueError:
        raise ValueError("'feature_log_return' not in features_list. 确保特征已正确命名")

    # 提取每个状态对应 'feature_log_return' 的均值，代表该状态的平均趋势
    state_means_log_return = model.means_[:, log_return_idx]
    
    # 根据趋势均值排序状态
    sorted_states = np.argsort(state_means_log_return)
    
    bear_state = sorted_states[0]  # 最低均值 -> 下跌
    neutral_state = sorted_states[1] # 中等均值 -> 盘整
    bull_state = sorted_states[2]  # 最高均值 -> 上涨
    
    state_map = {
        bull_state: "上涨",
        neutral_state: "盘整",
        bear_state: "下跌"
    }
    
    print("\n状态特征分析 (基于模型均值):")
    for state, name in state_map.items():
        mean_val = state_means_log_return[state]
        print(f"  状态{state} ({name}): 平均收益趋势 = {mean_val:.6f}")

    print(f"\n状态映射 (无前视偏差): 上涨={bull_state}, 盘整={neutral_state}, 下跌={bear_state}")
    
    return model, scaler, state_map

def backtest_v7_strategy(test_data: pd.DataFrame, model, scaler, state_map: dict, features_list: list) -> dict:
    """v7策略回测: 基于v5+v6成功逻辑的稳健版本"""
    
    print(f"\n--- v7策略回测 ---")
    
    test_data_copy = test_data.copy()
    
    # HMM状态预测
    X_test = scaler.transform(test_data_copy[features_list])
    states = model.predict(X_test)
    test_data_copy['hidden_state'] = states
    
    # 状态到交易信号映射
    test_data_copy['regime'] = test_data_copy['hidden_state'].map(state_map).fillna("盘整")
    
    # 生成交易信号 (延迟执行)
    signal_map = {"上涨": 1, "下跌": -1, "盘整": 0}
    test_data_copy['signal'] = test_data_copy['regime'].map(signal_map).shift(1).fillna(0)
    
    # 计算策略收益
    test_data_copy['strategy_return'] = test_data_copy['log_return'] * test_data_copy['signal']
    test_data_copy['benchmark_return'] = test_data_copy['log_return']
    
    # 性能指标计算 (已修正夏普比率)
    strategy_total_return = np.exp(test_data_copy['strategy_return'].sum()) - 1
    benchmark_total_return = np.exp(test_data_copy['benchmark_return'].sum()) - 1

    # 修正夏普比率计算：基于分钟数据进行精确年化
    # 黄金市场年交易分钟数 N = 252 (天) * 23 (小时) * 60 (分钟)
    minutes_in_year = 252 * 23 * 60 
    strategy_sharpe = (test_data_copy['strategy_return'].mean() / test_data_copy['strategy_return'].std() 
                     * np.sqrt(minutes_in_year) if test_data_copy['strategy_return'].std() > 0 else 0)
    
    benchmark_sharpe = (test_data_copy['benchmark_return'].mean() / test_data_copy['benchmark_return'].std() 
                      * np.sqrt(minutes_in_year) if test_data_copy['benchmark_return'].std() > 0 else 0)
    
    # 最大回撤
    cumulative_strategy = np.exp(test_data_copy['strategy_return'].cumsum())
    running_max = cumulative_strategy.expanding().max()
    drawdown = (cumulative_strategy - running_max) / running_max
    max_drawdown = drawdown.min()
    
    results = {
        'strategy_total_return': strategy_total_return,
        'benchmark_total_return': benchmark_total_return,
        'strategy_sharpe': strategy_sharpe,
        'benchmark_sharpe': benchmark_sharpe,
        'max_drawdown': max_drawdown,
        'outperformance': strategy_total_return - benchmark_total_return,
        'test_data': test_data_copy
    }
    
    print(f"\n🚀 HMM Strategy v7 性能总结:")
    print(f"v7策略收益: {strategy_total_return:.2%}")
    print(f"基准收益: {benchmark_total_return:.2%}")
    print(f"v7夏普比率: {strategy_sharpe:.2f}")
    print(f"基准夏普比率: {benchmark_sharpe:.2f}")
    print(f"策略超额收益: {strategy_total_return - benchmark_total_return:.2%}")
    print(f"最大回撤: {max_drawdown:.2%}")
    
    return results

def create_v7_visualization(results: dict):
    """v7结果可视化"""
    
    print(f"\n--- 生成v7分析图表 ---")
    
    test_data = results['test_data']
    
    fig, axes = plt.subplots(2, 2, figsize=(16, 10))
    fig.suptitle('HMM Strategy v7: 统一最优版本 (稳健改进)', fontsize=16, fontweight='bold')
    
    # 1. 累计收益对比
    ax1 = axes[0, 0]
    ax1.plot(np.exp(test_data['strategy_return'].cumsum()), 
            label='HMM Strategy v7', linewidth=2, color='darkred')
    ax1.plot(np.exp(test_data['benchmark_return'].cumsum()), 
            label='Buy & Hold', linestyle='--', alpha=0.7)
    ax1.axhline(y=1.0247, color='blue', linestyle=':', alpha=0.8, label='v5 Best (2.47%)')
    ax1.axhline(y=1.0223, color='green', linestyle=':', alpha=0.8, label='v6 Best (2.23%)')
    ax1.set_title('1. 累计收益对比')
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    
    # 2. 状态分布
    ax2 = axes[0, 1]
    regime_counts = test_data['regime'].value_counts()
    ax2.pie(regime_counts.values, labels=regime_counts.index, autopct='%1.1f%%')
    ax2.set_title('2. 市场状态分布')
    
    # 3. 收益分布对比
    ax3 = axes[1, 0]
    ax3.hist(test_data['strategy_return'], bins=50, alpha=0.6, label='v7策略', color='red')
    ax3.hist(test_data['benchmark_return'], bins=50, alpha=0.6, label='基准', color='blue')
    ax3.set_title('3. 收益分布对比')
    ax3.legend()
    ax3.grid(True, alpha=0.3)
    
    # 4. 性能指标对比
    ax4 = axes[1, 1]
    metrics = ['收益率(%)', '夏普比率', '最大回撤(%)']
    v5_values = [2.47, 11.091, -0.80]
    v6_values = [2.23, 13.46, 0] # 假设v6没有回撤数据
    v7_values = [results['strategy_total_return']*100, 
                results['strategy_sharpe'], 
                results['max_drawdown']*100]
    
    x = np.arange(len(metrics))
    width = 0.25
    
    ax4.bar(x - width, v5_values, width, label='v5', alpha=0.7)
    ax4.bar(x, v6_values, width, label='v6', alpha=0.7)  
    ax4.bar(x + width, v7_values, width, label='v7', alpha=0.7)
    
    ax4.set_title('4. 版本性能对比')
    ax4.set_xticks(x)
    ax4.set_xticklabels(metrics)
    ax4.legend()
    ax4.grid(True, alpha=0.3)
    
    plt.tight_layout(rect=[0, 0, 1, 0.95])
    plt.show()

def main():
    """v7主函数"""
    
    print("🚀 HMM Strategy v7: 统一最优版本")
    print("=" * 50)
    print("稳健改进: v6最优参数 + v5核心特征 + 简洁逻辑")
    print("目标: 稳定超越v6的13.46夏普比率")
    print("=" * 50)
    
    try:
        # 数据准备
        file_path = "/Users/<USER>/Documents/Git/quant-hmm/data/gold/1m.parquet"
        TRAIN_TEST_SPLIT_DATE = pd.to_datetime("2025-08-16", utc=True)
        
        df, features_list = load_and_prepare_data_v7(file_path)
        
        # 训练测试分割  
        train_data = df[df.index < TRAIN_TEST_SPLIT_DATE].copy()
        test_data = df[df.index >= TRAIN_TEST_SPLIT_DATE].copy()
        
        print(f"\n训练数据: {train_data.shape}")
        print(f"测试数据: {test_data.shape}")
        
        # v7模型训练
        model, scaler, state_map = train_hmm_v7(train_data, features_list)
        
        # v7策略回测
        results = backtest_v7_strategy(test_data, model, scaler, state_map, features_list)
        
        # 可视化结果
        create_v7_visualization(results)
        
        # v7 vs 历史版本对比
        print(f"\n🎯 v7 vs 历史版本总结:")
        print(f"v5 (智能平滑): 收益2.47%, 夏普11.091, 回撤-0.80%")
        print(f"v6 (MA优化):   收益2.23%, 夏普13.46")
        print(f"v7 (统一版本): 收益{results['strategy_total_return']:.2%}, 夏普{results['strategy_sharpe']:.2f}, 回撤{results['max_drawdown']:.2%}")
        
        # 成功判定
        if results['strategy_sharpe'] > 13.46:
            improvement = results['strategy_sharpe'] - 13.46
            print(f"\n🏆 SUCCESS! v7成功超越v6!")
            print(f"   夏普比率提升: +{improvement:.2f}")
            print(f"   稳健改进策略生效!")
        elif results['strategy_sharpe'] > 11.091:
            print(f"\n📊 GOOD: v7超越v5，接近v6水平")
            print(f"   夏普比率: {results['strategy_sharpe']:.2f} (v5: 11.091, v6: 13.46)")
        else:
            print(f"\n⚠️ ATTENTION: v7需要进一步优化")
            print(f"   当前夏普: {results['strategy_sharpe']:.2f} < v5: 11.091")
            
        print(f"\nv7设计验证:")
        print(f"✓ 使用v6最优MA(20,35)参数")
        print(f"✓ 精选5个核心特征 (避免过拟合)")
        print(f"✓ 稳健状态映射逻辑")
        print(f"✓ 保持代码简洁性")
        
    except Exception as e:
        print(f"\n❌ v7执行错误: {e}")
        raise

if __name__ == "__main__":
    main()