#!/usr/bin/env python3
"""
HMM Trading System Unit Tests - ASCII Version
Simple unit tests without Unicode characters for Windows compatibility
"""

import unittest
import pandas as pd
import numpy as np
from datetime import datetime, timezone, timedelta
import pytz
import json
import os
import sys
from unittest.mock import Mock, patch, MagicMock
import warnings
warnings.filterwarnings('ignore')

class TestBasicFunctionality(unittest.TestCase):
    """Test basic system functionality"""
    
    def test_timezone_handling(self):
        """Test timezone configuration"""
        local_tz = pytz.timezone('Asia/Shanghai')
        server_tz = pytz.timezone('Europe/Athens')
        
        self.assertIsNotNone(local_tz)
        self.assertIsNotNone(server_tz)
        
        # Test timezone conversion
        utc_time = datetime.now(timezone.utc)
        local_time = utc_time.astimezone(local_tz)
        
        # Should have timezone difference
        self.assertNotEqual(utc_time.hour, local_time.hour)
        print("PASSED: Timezone handling test")
    
    def test_config_loading(self):
        """Test configuration loading"""
        try:
            from config_loader import TradingConfig, TradingMode
            config = TradingConfig()
            
            self.assertEqual(config.symbol, "XAUUSD")
            self.assertEqual(config.trading_mode, TradingMode.PAPER)
            self.assertEqual(config.n_states, 3)
            print("PASSED: Configuration loading test")
            return True
        except Exception as e:
            print(f"FAILED: Configuration loading test - {e}")
            return False
    
    def test_data_features(self):
        """Test feature calculation"""
        try:
            from hmm_auto_trader_24x7 import DataManager, TradingConfig
            
            # Create test data
            dates = pd.date_range('2024-12-01', periods=100, freq='H')
            np.random.seed(42)
            prices = 2000 + np.cumsum(np.random.randn(100) * 0.1)
            
            df = pd.DataFrame({
                'Open': prices + np.random.randn(100) * 0.05,
                'High': prices + abs(np.random.randn(100) * 0.1),
                'Low': prices - abs(np.random.randn(100) * 0.1),
                'Close': prices,
                'Volume': np.random.randint(1000, 5000, 100)
            }, index=dates)
            
            # Test feature calculation
            config = TradingConfig()
            data_manager = DataManager(config, Mock(), Mock())
            result_df = data_manager._calculate_features(df)
            
            # Check features exist
            expected_features = ['log_return', 'momentum_5m', 'momentum_20m', 'price_position', 'ma_diff']
            for feature in expected_features:
                self.assertIn(feature, result_df.columns)
            
            print("PASSED: Feature calculation test")
            return True
        except Exception as e:
            print(f"FAILED: Feature calculation test - {e}")
            return False
    
    @patch('hmm_auto_trader_24x7.mt5')
    def test_mt5_connection_mock(self, mock_mt5):
        """Test MT5 connection with mocking"""
        try:
            from hmm_auto_trader_24x7 import MT5ConnectionManager, TradingConfig
            
            # Setup mock
            mock_mt5.initialize.return_value = True
            mock_mt5.symbol_info.return_value = Mock(visible=True)
            mock_mt5.symbol_info_tick.return_value = Mock(time=1701604800)
            
            config = TradingConfig()
            conn_manager = MT5ConnectionManager(config, Mock())
            
            result = conn_manager.connect()
            self.assertTrue(result)
            
            print("PASSED: MT5 connection mock test")
            return True
        except Exception as e:
            print(f"FAILED: MT5 connection mock test - {e}")
            return False
    
    def test_hmm_strategy_basic(self):
        """Test basic HMM strategy functionality"""
        try:
            from hmm_auto_trader_24x7 import HMMStrategyEngine, DataManager, TradingConfig
            
            # Create mock data manager with historical data
            config = TradingConfig()
            data_manager = Mock()
            
            # Create test data
            np.random.seed(42)
            dates = pd.date_range('2024-11-01', periods=1000, freq='H')
            prices = 2000 + np.cumsum(np.random.randn(1000) * 0.5)
            
            df = pd.DataFrame({
                'Open': prices + np.random.randn(1000) * 0.1,
                'High': prices + abs(np.random.randn(1000) * 0.2),
                'Low': prices - abs(np.random.randn(1000) * 0.2),
                'Close': prices,
                'Volume': np.random.randint(1000, 5000, 1000)
            }, index=dates)
            
            # Calculate features
            dm = DataManager(config, Mock(), Mock())
            df_with_features = dm._calculate_features(df).dropna()
            data_manager.historical_data = df_with_features
            
            # Test strategy engine
            strategy = HMMStrategyEngine(config, data_manager, Mock())
            
            # Test walk forward analysis
            result = strategy.walk_forward_analysis()
            self.assertTrue(result)
            
            print("PASSED: HMM strategy basic test")
            return True
        except Exception as e:
            print(f"FAILED: HMM strategy basic test - {e}")
            return False

def run_simple_tests():
    """Run simple test suite"""
    print("STEP 2: Unit Tests")
    print("=" * 40)
    
    # Create test suite
    suite = unittest.TestSuite()
    
    test_cases = [
        TestBasicFunctionality('test_timezone_handling'),
        TestBasicFunctionality('test_config_loading'),
        TestBasicFunctionality('test_data_features'),
        TestBasicFunctionality('test_mt5_connection_mock'),
        TestBasicFunctionality('test_hmm_strategy_basic'),
    ]
    
    for test_case in test_cases:
        suite.addTest(test_case)
    
    # Run tests
    runner = unittest.TextTestRunner(verbosity=0, stream=open(os.devnull, 'w'))
    result = runner.run(suite)
    
    # Print results
    total = result.testsRun
    passed = total - len(result.failures) - len(result.errors)
    
    print(f"Total tests: {total}")
    print(f"Passed: {passed}")
    print(f"Failed: {len(result.failures)}")
    print(f"Errors: {len(result.errors)}")
    
    if result.failures:
        print("\nFailures:")
        for test, traceback in result.failures:
            print(f"  - {test}: {traceback.split('AssertionError:')[-1].strip()}")
    
    if result.errors:
        print("\nErrors:")
        for test, traceback in result.errors:
            print(f"  - {test}: {traceback.split('Exception:')[-1].strip()}")
    
    success = len(result.failures) == 0 and len(result.errors) == 0
    print(f"\nRESULT: {'PASSED' if success else 'FAILED'}")
    
    return success

if __name__ == "__main__":
    try:
        success = run_simple_tests()
        sys.exit(0 if success else 1)
    except Exception as e:
        print(f"Test execution failed: {e}")
        sys.exit(1)