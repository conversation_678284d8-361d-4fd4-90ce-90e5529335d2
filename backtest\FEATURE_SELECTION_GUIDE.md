# 系统化特征选择框架使用指南

## 🎯 系统概述

这是一个专为HMM黄金交易策略设计的系统化特征选择框架，旨在科学、高效地评估和选择最优特征组合。

### 核心优势

1. **统一的特征库管理** - 避免重复代码，支持动态扩展
2. **标准化的回测流程** - 确保公平比较，消除偏差
3. **科学的统计分析** - 验证特征的真实效果和显著性
4. **自动化的测试流程** - 提高效率，减少人工错误
5. **可视化的结果展示** - 便于理解和决策

## 🏗️ 系统架构

```
特征选择系统
├── 特征库管理 (FeatureLibrary)
│   ├── 核心特征 (v0基准)
│   ├── 技术指标特征
│   ├── K线形态特征
│   └── 自定义特征接口
├── 标准化回测引擎 (StandardizedBacktestEngine)
│   ├── MT5数据获取
│   ├── HMM模型训练
│   ├── 前向分析
│   └── 性能指标计算
├── 特征分析器 (FeatureAnalyzer)
│   ├── 贡献度分析
│   ├── 统计显著性检验
│   ├── 相关性分析
│   └── 结果可视化
└── 控制器 (FeatureSelectionController)
    ├── 消融测试
    ├── 智能搜索
    ├── 自定义测试
    └── 结果管理
```

## 🚀 快速开始

### 1. 环境准备

确保已安装所需依赖：
```bash
pip install MetaTrader5 hmmlearn scikit-learn matplotlib seaborn scipy pandas numpy
```

### 2. 基本使用

#### 查看可用特征
```bash
python run_feature_selection.py --list-features
```

#### 运行特征消融测试
```bash
python run_feature_selection.py --mode ablation
```

#### 智能特征选择
```bash
python run_feature_selection.py --mode smart --strategy greedy --max-features 8
```

#### 测试自定义特征组合
```bash
python run_feature_selection.py --mode custom --features rsi,volatility,macd_strength
```

## 📋 详细功能说明

### 1. 特征消融测试 (Ablation Study)

**目的**: 科学验证单个特征的边际贡献

**原理**: 基于控制变量法，在v0基准特征基础上逐个添加新特征，观察效果变化

**使用场景**:
- 验证新特征的有效性
- 理解特征的边际贡献
- 为特征选择提供科学依据

**命令示例**:
```bash
# 测试所有可用特征
python run_feature_selection.py --mode ablation

# 测试指定特征
python run_feature_selection.py --mode ablation --features rsi,volatility

# 调整显著性水平
python run_feature_selection.py --mode ablation --significance-level 0.01
```

### 2. 智能特征选择

**目的**: 自动化寻找最优特征组合

**策略**:
- **贪心搜索**: 逐步添加最优特征，效率高
- **穷举搜索**: 测试所有可能组合，结果更全面

**使用场景**:
- 寻找最优特征组合
- 平衡特征数量和性能
- 自动化特征工程

**命令示例**:
```bash
# 贪心搜索 (推荐)
python run_feature_selection.py --mode smart --strategy greedy --max-features 8

# 穷举搜索 (小规模)
python run_feature_selection.py --mode smart --strategy exhaustive --max-features 6

# 调整改进阈值
python run_feature_selection.py --mode smart --min-improvement 0.05
```

### 3. 自定义测试

**目的**: 测试特定的特征组合

**使用场景**:
- 验证假设的特征组合
- 快速测试新想法
- 对比不同组合效果

**命令示例**:
```bash
# 测试技术指标组合
python run_feature_selection.py --mode custom --features rsi,macd_strength,volatility

# 测试K线形态组合
python run_feature_selection.py --mode custom --features trend_continuation,doji_pattern,gap_pattern
```

## 📊 内置特征库

### 基础特征 (v0核心)
- `feature_log_return`: 滞后对数收益率
- `momentum_5m`: 5分钟动量
- `momentum_20m`: 20分钟动量  
- `price_position`: 价格相对位置
- `ma_diff`: 均线差值

### 技术指标特征
- `rsi`: 相对强弱指标
- `volatility`: ATR波动率
- `momentum_acceleration`: 动量加速度
- `macd_strength`: MACD强度

### K线形态特征
- `body_ratio`: 实体大小比率
- `shadow_ratio`: 影线比率
- `gap_pattern`: 跳空形态
- `doji_pattern`: 十字星形态
- `trend_continuation`: 趋势延续形态

## 🔧 高级配置

### 系统参数调整

```bash
# 调整历史数据天数
python run_feature_selection.py --lookback-days 30

# 调整最大特征数
python run_feature_selection.py --max-features 10

# 调整统计显著性水平
python run_feature_selection.py --significance-level 0.01

# 调整最小改进阈值
python run_feature_selection.py --min-improvement 0.03

# 指定结果保存目录
python run_feature_selection.py --results-dir my_results

# 禁用图表生成
python run_feature_selection.py --no-plot
```

### 添加自定义特征

```python
from feature_selection_controller import FeatureSelectionController

# 初始化控制器
controller = FeatureSelectionController()

# 添加自定义特征
def my_custom_feature(df):
    return (df['Close'] / df['Open'] - 1).shift(1)

controller.add_custom_feature(
    name="custom_return",
    calculation_func=my_custom_feature,
    description="自定义收益率特征",
    category="custom"
)
```

## 📈 结果解读

### 输出文件

系统会在结果目录生成以下文件：
- `test_results_YYYYMMDD_HHMMSS.json`: 详细测试结果
- `feature_report_YYYYMMDD_HHMMSS.md`: 分析报告
- `feature_analysis_YYYYMMDD_HHMMSS.png`: 可视化图表

### 关键指标

1. **夏普比率**: 风险调整后收益，主要评估指标
2. **改进百分比**: 相对基准的改进幅度
3. **统计显著性**: p值，判断改进是否显著
4. **效应大小**: Cohen's d，量化改进的实际意义
5. **最大回撤**: 风险控制指标
6. **胜率**: 交易成功率

### 结果判断标准

- **显著改进**: p < 0.05 且改进 > 2%
- **边际改进**: 0.05 < p < 0.1 且改进 > 1%
- **无效特征**: p > 0.1 或改进 < 1%

## 🎯 最佳实践

### 1. 特征测试流程

```
1. 特征消融测试 → 识别有效特征
2. 相关性分析 → 去除冗余特征  
3. 智能组合搜索 → 找到最优组合
4. 统计验证 → 确认结果可靠性
5. 实盘验证 → 最终确认效果
```

### 2. 参数设置建议

- **数据天数**: 20-30天 (平衡数据量和时效性)
- **最大特征数**: 6-10个 (避免过拟合)
- **显著性水平**: 0.05 (标准统计水平)
- **改进阈值**: 2-5% (实际意义阈值)

### 3. 注意事项

1. **避免前视偏差**: 所有特征都已滞后1期处理
2. **数据质量**: 确保MT5连接稳定，数据完整
3. **统计意义**: 关注p值和效应大小，不只看改进幅度
4. **过拟合风险**: 控制特征数量，使用交叉验证
5. **实盘验证**: 回测结果需要实盘验证

## 🔍 故障排除

### 常见问题

1. **MT5连接失败**
   - 检查config.json配置
   - 确认MT5终端运行
   - 验证账户信息

2. **数据获取失败**
   - 检查网络连接
   - 确认交易品种正确
   - 调整数据天数

3. **特征计算错误**
   - 检查数据完整性
   - 验证特征定义
   - 查看错误日志

4. **回测失败**
   - 增加数据天数
   - 检查特征有效性
   - 调整HMM参数

### 调试模式

```bash
# 启用详细输出
python run_feature_selection.py --verbose

# 查看系统状态
python -c "from feature_selection_controller import FeatureSelectionController; c = FeatureSelectionController(); print(c.get_feature_summary())"
```

## 📞 技术支持

如遇问题，请检查：
1. 依赖包是否完整安装
2. MT5配置是否正确
3. 数据连接是否正常
4. 参数设置是否合理

系统设计遵循科学的量化研究方法论，确保特征选择的客观性和可靠性。
