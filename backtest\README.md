# HMM回测与特征选择模块

## 📁 目录结构

```
backtest/
├── README.md                           # 本文档 - 使用指南
├── feature_selection_framework.md      # 特征选择框架详细说明
├── feature_selection_analysis.md       # 方法对比分析报告
│
├── 🔬 特征选择工具 (三层架构)
│   ├── feature_ablation_test.py        # Layer 1: 特征消融测试
│   ├── feature_selector_smart.py       # Layer 2: 智能特征选择
│   └── feature_selector_full.py        # Layer 3: 完整自动化框架
│
└── 📊 HMM回测工具
    ├── hmm_backtest_v0.py              # v0基础版回测 (最优)
    └── hmm_backtest_v1_optimal.py      # v1优化版回测
```

## 🎯 快速开始

### 场景1: 验证新特征效果
```bash
# 使用特征消融测试 (推荐)
python feature_ablation_test.py
```

### 场景2: 日常特征优化
```bash
# 使用智能特征选择器
python feature_selector_smart.py
```

### 场景3: 学术研究
```bash
# 使用完整自动化框架
python feature_selector_full.py
```

### 场景4: HMM回测
```bash
# v0基础版 (最优效果)
python hmm_backtest_v0.py

# v1优化版 (研究用)
python hmm_backtest_v1_optimal.py
```

## 🔬 特征选择工具详解

### Layer 1: 特征消融测试 (`feature_ablation_test.py`)

**原理**: 控制变量法，逐个测试特征的边际贡献

**优势**:
- ✅ 科学严谨，结果可解释
- ✅ 执行效率高 (0.58组合/秒)
- ✅ 符合实验科学原理

**使用场景**:
- 验证特定特征假设
- 解释特征选择结果
- 快速效果验证

**示例结果**:
```
v0基础版: 7.48 (基准)
v0 + 趋势延续: 7.48 (+0.0%)
v0 + RSI: 6.00 (-19.8%)
```

### Layer 2: 智能特征选择 (`feature_selector_smart.py`)

**原理**: 基于Random Forest重要性的智能搜索策略

**优势**:
- ✅ 平衡自动化与效率
- ✅ 依赖少，易部署
- ✅ 适合生产环境

**使用场景**:
- 日常系统优化
- 特征组合搜索
- 生产环境集成

**核心算法**:
1. Random Forest重要性排序
2. 多策略组合搜索
3. 时间序列交叉验证
4. 智能早停机制

### Layer 3: 完整自动化框架 (`feature_selector_full.py`)

**原理**: 多算法融合的理论完备解决方案

**算法组合**:
- RFE (递归特征消除)
- LASSO (L1正则化)
- Boruta (全相关特征选择)
- Sequential (前向/后向选择)

**使用场景**:
- 学术研究
- 方法论验证
- 大规模特征工程

**注意**: 需要安装额外依赖 `pip install boruta optuna`

## 📊 性能对比总结

| 工具 | 效果 | 效率 | 易用性 | 适用场景 |
|------|------|------|--------|----------|
| **消融测试** | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ | 快速验证 |
| **智能选择** | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐ | ⭐⭐⭐⭐ | 日常优化 |
| **完整框架** | ⭐⭐⭐⭐⭐ | ⭐⭐ | ⭐⭐ | 学术研究 |

## 🏆 最佳实践

### 1. 新项目启动流程
```
1. feature_ablation_test.py    验证核心假设
2. feature_selector_smart.py   优化特征组合
3. hmm_backtest_v0.py          验证最终效果
```

### 2. 日常维护流程
```
1. feature_selector_smart.py   定期优化
2. feature_ablation_test.py    解释异常
3. 更新生产配置
```

### 3. 研究开发流程
```
1. feature_selector_full.py    全面分析
2. feature_ablation_test.py    验证关键发现
3. 发表研究成果
```

## ⚙️ 配置要求

### 基础依赖 (所有工具)
```bash
pip install numpy pandas scikit-learn hmmlearn
```

### 扩展依赖 (完整框架)
```bash
pip install boruta optuna
```

### MT5连接
确保 `config.json` 配置正确:
```json
{
  "mt5_path": "C:/Program Files/MetaTrader 5/terminal64.exe",
  "login_id": 你的账户ID,
  "password": "你的密码",
  "server_name": "你的服务器"
}
```

## 📈 核心发现

通过全面测试验证，我们发现：

1. **v0的5个特征是最优组合**:
   ```python
   optimal_features = [
       'feature_log_return',  # 价格变化
       'momentum_5m',         # 短期动量
       'momentum_20m',        # 中期动量
       'price_position',      # Williams %R价格位置
       'ma_diff'             # 趋势方向
   ]
   ```

2. **"少即是多"原理得到验证**: 添加更多特征会降低HMM性能

3. **简单方法在实际应用中更有效**: 手动消融测试效率最高

## 🔧 故障排除

### 常见问题

**Q: 特征选择器运行失败**
```bash
# 检查数据质量
python -c "import pandas as pd; print('数据检查通过')"
```

**Q: MT5连接失败**
```bash
# 检查配置文件
python -c "from config_loader import load_config; print('配置加载成功')"
```

**Q: 依赖包缺失**
```bash
# 安装所需依赖
pip install -r requirements.txt
```

## 📚 进一步阅读

- `feature_selection_framework.md` - 详细的理论框架说明
- `feature_selection_analysis.md` - 深度对比分析报告

## 🤝 贡献指南

当添加新的特征选择方法时：
1. 保持三层架构的设计原则
2. 确保代码风格一致
3. 添加相应的测试和文档
4. 通过消融测试验证效果

---

*该模块体现了工程实践中的平衡艺术：科学性与实用性、完备性与效率性、理论性与可操作性的完美结合。*