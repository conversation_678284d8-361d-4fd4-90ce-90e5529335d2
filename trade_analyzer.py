#!/usr/bin/env python3
"""
HMM量化交易系统 - 统一交易分析工具
专业全方位分析交易记录，为策略迭代提供精确数据支持
"""

import MetaTrader5 as mt5
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import json
import os
import re
from typing import Dict, List, Optional

class UnifiedTradeAnalyzer:
    """统一交易分析器"""
    
    def __init__(self, symbol: str = "XAUUSD"):
        self.symbol = symbol
        self.output_dir = "logs/trade_analysis"
        os.makedirs(self.output_dir, exist_ok=True)
        
    def connect_mt5(self) -> bool:
        if not mt5.initialize():
            print(f"MT5连接失败: {mt5.last_error()}")
            return False
        return True
    
    def get_paired_trades(self, days_back: int = 30) -> pd.DataFrame:
        """获取配对的完整交易记录"""
        if not self.connect_mt5():
            return pd.DataFrame()
        
        end_time = datetime.now()
        start_time = end_time - timedelta(days=days_back)
        
        deals = mt5.history_deals_get(start_time, end_time)
        if not deals:
            print(f"未找到 {days_back} 天内的交易记录")
            return pd.DataFrame()
        
        df = pd.DataFrame(list(deals), columns=deals[0]._asdict().keys())
        df = df[df['symbol'] == self.symbol].copy()
        df['time'] = pd.to_datetime(df['time'], unit='s')
        df = df.sort_values('time').reset_index(drop=True)
        
        # 配对开仓和平仓
        paired_trades = []
        open_positions = {}
        
        for _, deal in df.iterrows():
            pos_id = deal['position_id']
            
            if deal['entry'] == 0:  # 开仓
                open_positions[pos_id] = deal
            elif deal['entry'] == 1 and pos_id in open_positions:  # 平仓
                open_deal = open_positions[pos_id]
                
                duration_minutes = (deal['time'] - open_deal['time']).total_seconds() / 60
                price_diff = deal['price'] - open_deal['price']
                if open_deal['type'] == 1:  # SELL
                    price_diff = -price_diff
                
                paired_trades.append({
                    'position_id': pos_id,
                    'open_time': open_deal['time'],
                    'close_time': deal['time'],
                    'duration_minutes': duration_minutes,
                    'trade_type': 'BUY' if open_deal['type'] == 0 else 'SELL',
                    'volume': open_deal['volume'],
                    'open_price': open_deal['price'],
                    'close_price': deal['price'],
                    'price_diff': price_diff,
                    'profit': deal['profit'],
                    'commission': open_deal['commission'] + deal['commission'],
                    'swap': open_deal['swap'] + deal['swap'],
                    'net_profit': deal['profit'] - abs(open_deal['commission'] + deal['commission'])
                })
                
                del open_positions[pos_id]
        
        return pd.DataFrame(paired_trades)
    
    def parse_hmm_logs(self) -> Dict:
        """解析HMM交易日志，提取状态和信号信息"""
        log_data = {'signals': [], 'states': [], 'kelly_calcs': [], 'orders': []}
        
        log_files = []
        if os.path.exists('logs'):
            for file in os.listdir('logs'):
                if file.startswith('hmm_trader_') and file.endswith('.log'):
                    log_files.append(os.path.join('logs', file))
        
        if not log_files:
            return log_data
        
        latest_log = max(log_files, key=os.path.getmtime)
        
        try:
            with open(latest_log, 'r', encoding='utf-8') as f:
                for line in f:
                    line = line.strip()
                    
                    # 信号变化
                    if '信号变化:' in line:
                        match = re.search(r'(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}),\d+ \| INFO \| HMMAutoTrader \| 信号变化: (.+)', line)
                        if match:
                            log_data['signals'].append({
                                'timestamp': datetime.strptime(match.group(1), '%Y-%m-%d %H:%M:%S'),
                                'signal_change': match.group(2)
                            })
                    
                    # 状态变化
                    elif '状态变化:' in line:
                        match = re.search(r'(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}),\d+ \| INFO \| HMMAutoTrader \| 状态变化: (.+)', line)
                        if match:
                            log_data['states'].append({
                                'timestamp': datetime.strptime(match.group(1), '%Y-%m-%d %H:%M:%S'),
                                'state_change': match.group(2)
                            })
                    
                    # Kelly计算
                    elif 'Kelly仓位:' in line:
                        match = re.search(r'(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}),\d+ \| INFO \| HMMAutoTrader \| Kelly仓位: (.+)', line)
                        if match:
                            log_data['kelly_calcs'].append({
                                'timestamp': datetime.strptime(match.group(1), '%Y-%m-%d %H:%M:%S'),
                                'kelly_info': match.group(2)
                            })
                    
                    # 开仓记录
                    elif '开仓成功:' in line:
                        match = re.search(r'(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}),\d+ \| INFO \| HMMAutoTrader \| 开仓成功: (.+)', line)
                        if match:
                            log_data['orders'].append({
                                'timestamp': datetime.strptime(match.group(1), '%Y-%m-%d %H:%M:%S'),
                                'order_info': match.group(2)
                            })
        
        except Exception as e:
            print(f"日志解析失败: {e}")
        
        return log_data
    
    def analyze_trade_performance(self, df: pd.DataFrame) -> Dict:
        """分析交易表现"""
        if df.empty:
            return {"error": "无交易数据"}
        
        total_trades = len(df)
        winning_trades = df[df['profit'] > 0]
        losing_trades = df[df['profit'] <= 0]
        
        win_rate = len(winning_trades) / total_trades
        avg_win = winning_trades['profit'].mean() if len(winning_trades) > 0 else 0
        avg_loss = losing_trades['profit'].mean() if len(losing_trades) > 0 else 0
        profit_factor = abs(winning_trades['profit'].sum() / losing_trades['profit'].sum()) if len(losing_trades) > 0 else float('inf')
        
        # 方向性分析
        buy_trades = df[df['trade_type'] == 'BUY']
        sell_trades = df[df['trade_type'] == 'SELL']
        buy_win_rate = len(buy_trades[buy_trades['profit'] > 0]) / len(buy_trades) if len(buy_trades) > 0 else 0
        sell_win_rate = len(sell_trades[sell_trades['profit'] > 0]) / len(sell_trades) if len(sell_trades) > 0 else 0
        
        # 时间分析
        avg_duration = df['duration_minutes'].mean()
        win_duration = winning_trades['duration_minutes'].mean() if len(winning_trades) > 0 else 0
        loss_duration = losing_trades['duration_minutes'].mean() if len(losing_trades) > 0 else 0
        
        return {
            "总交易数": total_trades,
            "胜率": win_rate,
            "平均盈利": avg_win,
            "平均亏损": avg_loss,
            "盈亏比": abs(avg_win / avg_loss) if avg_loss != 0 else 0,
            "利润因子": profit_factor,
            "总盈亏": df['profit'].sum(),
            "平均持仓时间": avg_duration,
            "盈利交易平均持仓": win_duration,
            "亏损交易平均持仓": loss_duration,
            "BUY交易数": len(buy_trades),
            "SELL交易数": len(sell_trades),
            "BUY胜率": buy_win_rate,
            "SELL胜率": sell_win_rate,
            "最大盈利": df['profit'].max(),
            "最大亏损": df['profit'].min(),
        }
    
    def identify_problems(self, df: pd.DataFrame, performance: Dict) -> List[str]:
        """识别策略问题"""
        problems = []
        
        # 过度交易
        if performance.get("平均持仓时间", 0) < 30:
            problems.append(f"过度交易: 平均持仓{performance['平均持仓时间']:.1f}分钟，建议>30分钟")
        
        # 胜率问题
        if performance.get("胜率", 0) < 0.4:
            problems.append(f"胜率偏低: {performance['胜率']:.1%}，建议优化信号质量")
        
        # 方向性偏差
        buy_win_rate = performance.get("BUY胜率", 0)
        sell_win_rate = performance.get("SELL胜率", 0)
        if abs(buy_win_rate - sell_win_rate) > 0.15:
            if buy_win_rate > sell_win_rate:
                problems.append(f"SELL信号质量差: SELL胜率{sell_win_rate:.1%} vs BUY胜率{buy_win_rate:.1%}")
            else:
                problems.append(f"BUY信号质量差: BUY胜率{buy_win_rate:.1%} vs SELL胜率{sell_win_rate:.1%}")
        
        # 盈亏比问题
        if performance.get("盈亏比", 0) < 1.5:
            problems.append(f"盈亏比偏低: {performance['盈亏比']:.2f}，建议调整止盈止损")
        
        return problems
    
    def generate_optimization_suggestions(self, problems: List[str], performance: Dict) -> List[str]:
        """生成优化建议"""
        suggestions = []
        
        for problem in problems:
            if "过度交易" in problem:
                suggestions.append("增加最小持仓时间限制或信号稳定性过滤")
            elif "胜率偏低" in problem:
                suggestions.append("优化HMM状态识别参数或增加确认信号")
            elif "SELL信号质量差" in problem:
                suggestions.append("重点检查空头信号逻辑，可能需要调整SELL状态映射")
            elif "BUY信号质量差" in problem:
                suggestions.append("重点检查多头信号逻辑，可能需要调整BUY状态映射")
            elif "盈亏比偏低" in problem:
                suggestions.append("调整止盈止损比例，考虑从200/400点调整为150/450点")
        
        # 基于整体表现的建议
        if performance.get("利润因子", 0) < 1.2:
            suggestions.append("整体盈利能力偏弱，建议暂停实盘，优化策略参数")
        
        return suggestions
    
    def correlate_signals_trades(self, trades_df: pd.DataFrame, log_data: Dict) -> List[Dict]:
        """关联信号与交易执行"""
        correlations = []
        
        for _, trade in trades_df.iterrows():
            trade_time = trade['open_time']
            
            # 找最近的信号
            recent_signal = "未找到"
            for signal in reversed(log_data['signals']):
                if signal['timestamp'] <= trade_time:
                    recent_signal = signal['signal_change']
                    break
            
            # 找最近的状态
            recent_state = "未找到"
            for state in reversed(log_data['states']):
                if state['timestamp'] <= trade_time:
                    recent_state = state['state_change']
                    break
            
            # 找对应的Kelly计算
            recent_kelly = "未找到"
            for kelly in log_data['kelly_calcs']:
                if abs((kelly['timestamp'] - trade_time).total_seconds()) < 120:
                    recent_kelly = kelly['kelly_info']
                    break
            
            correlations.append({
                "开仓时间": trade_time,
                "交易类型": trade['trade_type'],
                "持仓时间": f"{trade['duration_minutes']:.1f}分钟",
                "开仓价": trade['open_price'],
                "平仓价": trade['close_price'],
                "盈亏": trade['profit'],
                "信号": recent_signal,
                "状态": recent_state,
                "Kelly": recent_kelly
            })
        
        return correlations
    
    def run_comprehensive_analysis(self, days_back: int = 30) -> Dict:
        """运行综合分析"""
        print(f"开始分析最近 {days_back} 天的交易记录...")
        
        # 获取数据
        trades_df = self.get_paired_trades(days_back)
        log_data = self.parse_hmm_logs()
        
        if trades_df.empty:
            return {"error": "无交易数据"}
        
        print(f"找到 {len(trades_df)} 笔完整交易")
        
        # 执行分析
        performance = self.analyze_trade_performance(trades_df)
        problems = self.identify_problems(trades_df, performance)
        suggestions = self.generate_optimization_suggestions(problems, performance)
        correlations = self.correlate_signals_trades(trades_df, log_data)
        
        # 最差和最佳交易
        worst_trades = trades_df.nsmallest(5, 'profit')[['open_time', 'trade_type', 'open_price', 'close_price', 'duration_minutes', 'profit']].to_dict('records')
        best_trades = trades_df.nlargest(5, 'profit')[['open_time', 'trade_type', 'open_price', 'close_price', 'duration_minutes', 'profit']].to_dict('records')
        
        # 生成报告
        report = {
            "分析时间": datetime.now().isoformat(),
            "分析周期": f"最近{days_back}天",
            "交易品种": self.symbol,
            "核心指标": performance,
            "识别问题": problems,
            "优化建议": suggestions,
            "信号交易关联": correlations,
            "最差5笔交易": worst_trades,
            "最佳5笔交易": best_trades
        }
        
        return report
    
    def print_analysis_summary(self, report: Dict):
        """打印分析摘要"""
        if "error" in report:
            print(f"分析失败: {report['error']}")
            return
        
        perf = report["核心指标"]
        
        print("\n" + "="*80)
        print("HMM量化交易系统 - 专业交易分析报告")
        print("="*80)
        
        print(f"\n[核心指标]")
        print(f"总交易数: {perf['总交易数']}")
        print(f"胜率: {perf['胜率']:.1%}")
        print(f"盈亏比: {perf['盈亏比']:.2f}")
        print(f"利润因子: {perf['利润因子']:.2f}")
        print(f"总盈亏: ${perf['总盈亏']:.2f}")
        print(f"平均持仓: {perf['平均持仓时间']:.1f}分钟")
        print(f"最大盈利: ${perf['最大盈利']:.2f}")
        print(f"最大亏损: ${perf['最大亏损']:.2f}")
        
        print(f"\n[方向性分析]")
        print(f"BUY: {perf['BUY交易数']}笔, 胜率{perf['BUY胜率']:.1%}")
        print(f"SELL: {perf['SELL交易数']}笔, 胜率{perf['SELL胜率']:.1%}")
        
        print(f"\n[问题识别]")
        for problem in report["识别问题"]:
            print(f"- {problem}")
        
        print(f"\n[优化建议]")
        for i, suggestion in enumerate(report["优化建议"], 1):
            print(f"{i}. {suggestion}")
        
        print(f"\n[最差5笔交易]")
        for i, trade in enumerate(report["最差5笔交易"], 1):
            print(f"{i}. {trade['open_time'].strftime('%m-%d %H:%M')} | {trade['trade_type']} | {trade['open_price']:.2f}->{trade['close_price']:.2f} | {trade['duration_minutes']:.1f}min | ${trade['profit']:.2f}")
        
        print(f"\n[最佳5笔交易]")
        for i, trade in enumerate(report["最佳5笔交易"], 1):
            print(f"{i}. {trade['open_time'].strftime('%m-%d %H:%M')} | {trade['trade_type']} | {trade['open_price']:.2f}->{trade['close_price']:.2f} | {trade['duration_minutes']:.1f}min | ${trade['profit']:.2f}")
        
        print("="*80)
    
    def save_analysis_report(self, report: Dict):
        """保存分析报告"""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = os.path.join(self.output_dir, f"trade_analysis_{timestamp}.json")
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2, default=str)
        
        print(f"\n详细报告已保存到: {filename}")
        return filename
    
    def analyze(self, days_back: int = 30):
        """执行完整分析流程"""
        report = self.run_comprehensive_analysis(days_back)
        self.print_analysis_summary(report)
        self.save_analysis_report(report)
        return report

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='HMM量化交易系统统一分析工具')
    parser.add_argument('--days', '-d', type=int, default=30, help='分析天数')
    parser.add_argument('--symbol', '-s', default='XAUUSD', help='交易品种')
    
    args = parser.parse_args()
    
    analyzer = UnifiedTradeAnalyzer(symbol=args.symbol)
    analyzer.analyze(days_back=args.days)

if __name__ == "__main__":
    main()
