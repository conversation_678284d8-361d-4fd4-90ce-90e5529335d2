#!/usr/bin/env python3
"""
HMM自动化交易系统试运行测试脚本
在不执行实际交易的情况下测试系统的完整运行流程
"""

import sys
import time
import threading
from datetime import datetime, timedelta
from unittest.mock import Mock, patch, MagicMock
import numpy as np
import pandas as pd

# 导入系统组件
from config_loader import load_config, TradingConfig, TradingMode
from hmm_auto_trader_24x7 import HMMAutoTradingSystem, SignalType

class DryRunTester:
    """试运行测试器"""
    
    def __init__(self):
        self.config = TradingConfig()
        self.config.trading_mode = TradingMode.PAPER  # 强制使用模拟模式
        self.system = None
        self.test_results = {
            'connection_tests': [],
            'data_tests': [],
            'strategy_tests': [],
            'signal_tests': [],
            'risk_tests': [],
            'trade_tests': []
        }
    
    def setup_mock_mt5(self):
        """设置MT5模拟环境"""
        print("🔧 设置MT5模拟环境...")
        
        # 创建模拟的MT5模块
        mock_mt5 = MagicMock()
        
        # 模拟连接成功
        mock_mt5.initialize.return_value = True
        mock_mt5.last_error.return_value = (0, 'Success')
        
        # 模拟品种信息
        mock_symbol_info = Mock()
        mock_symbol_info.visible = True
        mock_symbol_info.spread = 3
        mock_symbol_info.point = 0.01
        mock_symbol_info.digits = 2
        mock_symbol_info.volume_min = 0.01
        mock_symbol_info.volume_max = 100.0
        mock_symbol_info.volume_step = 0.01
        mock_symbol_info.trade_contract_size = 100
        mock_mt5.symbol_info.return_value = mock_symbol_info
        mock_mt5.symbol_select.return_value = True
        
        # 模拟账户信息
        mock_account = Mock()
        mock_account.login = ********
        mock_account.balance = 10000.0
        mock_account.equity = 10000.0
        mock_account.margin = 1000.0
        mock_account.margin_free = 9000.0
        mock_account.margin_level = 1000.0
        mock_mt5.account_info.return_value = mock_account
        
        # 模拟实时报价
        mock_tick = Mock()
        mock_tick.bid = 2048.50
        mock_tick.ask = 2048.53
        mock_tick.time = int(datetime.now().timestamp())
        mock_mt5.symbol_info_tick.return_value = mock_tick
        
        # 模拟历史数据
        mock_mt5.copy_rates_range.side_effect = self._generate_mock_rates
        mock_mt5.copy_rates_from_pos.side_effect = self._generate_mock_rates_pos
        
        # 模拟持仓和交易
        mock_mt5.positions_get.return_value = []
        mock_mt5.order_send.return_value = Mock(retcode=10009, order=123456)  # TRADE_RETCODE_DONE
        mock_mt5.TRADE_RETCODE_DONE = 10009
        mock_mt5.ORDER_TYPE_BUY = 0
        mock_mt5.ORDER_TYPE_SELL = 1
        mock_mt5.TRADE_ACTION_DEAL = 1
        mock_mt5.ORDER_TIME_GTC = 0
        mock_mt5.ORDER_FILLING_IOC = 1
        mock_mt5.TIMEFRAME_M1 = 1
        
        return mock_mt5
    
    def _generate_mock_rates(self, symbol, timeframe, start_date, end_date):
        """生成模拟历史数据"""
        # 计算需要的数据点数量
        if hasattr(start_date, 'timestamp'):
            start_ts = int(start_date.timestamp())
            end_ts = int(end_date.timestamp())
        else:
            start_ts = int(start_date)
            end_ts = int(end_date)
        
        # 生成1分钟K线数据
        time_points = list(range(start_ts, end_ts, 60))  # 每分钟
        n_points = len(time_points)
        
        if n_points == 0:
            return None
        
        # 限制数据量
        if n_points > 3000:
            time_points = time_points[-3000:]
            n_points = 3000
        
        # 生成价格序列
        np.random.seed(42)
        base_price = 2000.0
        price_changes = np.random.randn(n_points) * 0.5
        prices = base_price + np.cumsum(price_changes)
        
        rates = []
        for i, ts in enumerate(time_points):
            price = prices[i]
            rates.append({
                'time': ts,
                'open': price + np.random.randn() * 0.1,
                'high': price + abs(np.random.randn()) * 0.3,
                'low': price - abs(np.random.randn()) * 0.3,
                'close': price,
                'tick_volume': np.random.randint(100, 1000),
                'spread': 3,
                'real_volume': 0
            })
        
        return np.array(rates, dtype=object) if rates else None
    
    def _generate_mock_rates_pos(self, symbol, timeframe, start_pos, count):
        """生成指定位置的模拟数据"""
        current_time = int(datetime.now().timestamp())
        
        rates = []
        np.random.seed(42 + start_pos)
        
        for i in range(count):
            ts = current_time - (start_pos + count - i) * 60  # 倒推时间
            base_price = 2050.0 + np.random.randn() * 2
            
            rates.append({
                'time': ts,
                'open': base_price + np.random.randn() * 0.1,
                'high': base_price + abs(np.random.randn()) * 0.3,
                'low': base_price - abs(np.random.randn()) * 0.3,
                'close': base_price,
                'tick_volume': np.random.randint(100, 1000),
                'spread': 3,
                'real_volume': 0
            })
        
        return np.array(rates, dtype=object) if rates else None
    
    def test_connection_management(self, mock_mt5):
        """测试连接管理"""
        print("\n🔌 测试MT5连接管理...")
        
        try:
            # 测试正常连接
            with patch('hmm_auto_trader_24x7.mt5', mock_mt5):
                result = self.system.connection_manager.connect()
                self.test_results['connection_tests'].append(('正常连接', result))
                print(f"  ✅ 正常连接: {'成功' if result else '失败'}")
                
                # 测试心跳检测
                heartbeat_result = self.system.connection_manager.heartbeat_check()
                self.test_results['connection_tests'].append(('心跳检测', heartbeat_result))
                print(f"  ✅ 心跳检测: {'正常' if heartbeat_result else '失败'}")
                
                # 测试重连逻辑
                self.system.connection_manager.connected = False
                mock_mt5.initialize.return_value = True  # 重连成功
                reconnect_result = self.system.connection_manager.reconnect()
                self.test_results['connection_tests'].append(('重连逻辑', reconnect_result))
                print(f"  ✅ 重连逻辑: {'成功' if reconnect_result else '失败'}")
                
        except Exception as e:
            print(f"  ❌ 连接测试异常: {e}")
            return False
        
        return True
    
    def test_data_management(self, mock_mt5):
        """测试数据管理"""
        print("\n📊 测试数据管理...")
        
        try:
            with patch('hmm_auto_trader_24x7.mt5', mock_mt5):
                # 测试历史数据加载
                data_loaded = self.system.data_manager.load_historical_data()
                self.test_results['data_tests'].append(('历史数据加载', data_loaded))
                print(f"  ✅ 历史数据加载: {'成功' if data_loaded else '失败'}")
                
                if data_loaded:
                    data_length = len(self.system.data_manager.historical_data)
                    print(f"     加载数据量: {data_length} 条")
                    
                    # 测试特征计算
                    features_ok = all(
                        col in self.system.data_manager.historical_data.columns 
                        for col in self.system.data_manager.features
                    )
                    self.test_results['data_tests'].append(('特征计算', features_ok))
                    print(f"  ✅ 特征计算: {'完整' if features_ok else '缺失'}")
                    
                    # 测试最新K线获取
                    latest_candle = self.system.data_manager.get_latest_candle()
                    candle_ok = latest_candle is not None
                    self.test_results['data_tests'].append(('实时K线获取', candle_ok))
                    print(f"  ✅ 实时K线获取: {'成功' if candle_ok else '失败'}")
                    
                    if candle_ok:
                        print(f"     最新K线: {latest_candle['close']:.2f}")
                
        except Exception as e:
            print(f"  ❌ 数据管理测试异常: {e}")
            return False
        
        return True
    
    def test_strategy_engine(self, mock_mt5):
        """测试策略引擎"""
        print("\n🧠 测试HMM策略引擎...")
        
        try:
            with patch('hmm_auto_trader_24x7.mt5', mock_mt5):
                # 测试前向分析
                print("  执行前向展开分析...")
                forward_analysis_ok = self.system.strategy_engine.walk_forward_analysis()
                self.test_results['strategy_tests'].append(('前向分析', forward_analysis_ok))
                print(f"  ✅ 前向分析: {'成功' if forward_analysis_ok else '失败'}")
                
                if forward_analysis_ok:
                    best_ratio = self.system.strategy_engine.best_params.get('train_ratio', 0)
                    sharpe = self.system.strategy_engine.best_params.get('sharpe_ratio', 0)
                    print(f"     最佳训练比例: {best_ratio:.3f}")
                    print(f"     夏普比率: {sharpe:.3f}")
                
                # 测试模型训练
                print("  训练HMM模型...")
                model_trained = self.system.strategy_engine.train_model()
                self.test_results['strategy_tests'].append(('模型训练', model_trained))
                print(f"  ✅ 模型训练: {'成功' if model_trained else '失败'}")
                
                if model_trained:
                    print(f"     状态映射: {self.system.strategy_engine.state_map}")
                
        except Exception as e:
            print(f"  ❌ 策略引擎测试异常: {e}")
            return False
        
        return True
    
    def test_signal_generation(self, mock_mt5):
        """测试信号生成"""
        print("\n📡 测试信号生成...")
        
        try:
            with patch('hmm_auto_trader_24x7.mt5', mock_mt5):
                # 生成多个测试信号
                signals_generated = []
                
                for i in range(5):
                    # 模拟新K线
                    base_price = 2050 + i * 0.5
                    test_candle = {
                        'datetime': datetime.now() + timedelta(minutes=i),
                        'open': base_price,
                        'high': base_price + 0.3,
                        'low': base_price - 0.2,
                        'close': base_price + 0.1,
                        'volume': 1000 + i * 100
                    }
                    
                    signal, reason = self.system.strategy_engine.generate_signal(test_candle)
                    signals_generated.append((signal, reason))
                    print(f"  K线{i+1}: {signal.name} - {reason}")
                
                signals_ok = len(signals_generated) > 0
                self.test_results['signal_tests'].append(('信号生成', signals_ok))
                print(f"  ✅ 信号生成: {'正常' if signals_ok else '异常'}")
                
                # 统计信号分布
                signal_counts = {}
                for signal, _ in signals_generated:
                    signal_counts[signal.name] = signal_counts.get(signal.name, 0) + 1
                
                print(f"  信号分布: {signal_counts}")
                
        except Exception as e:
            print(f"  ❌ 信号生成测试异常: {e}")
            return False
        
        return True
    
    def test_risk_management(self, mock_mt5):
        """测试风险管理"""
        print("\n⚠️ 测试风险管理...")
        
        try:
            with patch('hmm_auto_trader_24x7.mt5', mock_mt5):
                # 测试交易权限检查
                can_trade, reason = self.system.risk_manager.can_trade()
                self.test_results['risk_tests'].append(('交易权限检查', can_trade))
                print(f"  ✅ 交易权限检查: {'允许' if can_trade else '拒绝'} - {reason}")
                
                # 测试仓位计算
                for signal_type in [SignalType.BUY, SignalType.SELL]:
                    position_size = self.system.risk_manager.calculate_position_size(signal_type)
                    size_ok = 0 < position_size <= self.config.max_position_size
                    self.test_results['risk_tests'].append((f'仓位计算_{signal_type.name}', size_ok))
                    print(f"  ✅ {signal_type.name}仓位计算: {position_size:.3f}手 ({'合理' if size_ok else '异常'})")
                
                # 测试风险限制（模拟高风险情况）
                original_balance = mock_mt5.account_info.return_value.balance
                mock_mt5.account_info.return_value.balance = 100  # 模拟低余额
                mock_mt5.account_info.return_value.equity = 50    # 模拟高亏损
                
                can_trade_risk, risk_reason = self.system.risk_manager.can_trade()
                self.test_results['risk_tests'].append(('风险限制检查', not can_trade_risk))
                print(f"  ✅ 风险限制检查: {'触发保护' if not can_trade_risk else '未触发'} - {risk_reason}")
                
                # 恢复正常状态
                mock_mt5.account_info.return_value.balance = original_balance
                mock_mt5.account_info.return_value.equity = original_balance
                
        except Exception as e:
            print(f"  ❌ 风险管理测试异常: {e}")
            return False
        
        return True
    
    def test_trade_execution(self, mock_mt5):
        """测试交易执行（模拟模式）"""
        print("\n💼 测试交易执行...")
        
        try:
            with patch('hmm_auto_trader_24x7.mt5', mock_mt5):
                # 测试各种信号的执行
                test_signals = [
                    (SignalType.BUY, "测试买入信号"),
                    (SignalType.SELL, "测试卖出信号"),
                    (SignalType.HOLD, "测试持有信号")
                ]
                
                for signal, reason in test_signals:
                    result = self.system.trade_executor.execute_signal(signal, reason)
                    self.test_results['trade_tests'].append((f'信号执行_{signal.name}', result))
                    print(f"  ✅ {signal.name}信号执行: {'成功' if result else '失败'} - {reason}")
                
                # 测试持仓获取
                positions = self.system.trade_executor._get_positions()
                positions_ok = positions is None or isinstance(positions, dict)
                self.test_results['trade_tests'].append(('持仓查询', positions_ok))
                print(f"  ✅ 持仓查询: {'正常' if positions_ok else '异常'}")
                
        except Exception as e:
            print(f"  ❌ 交易执行测试异常: {e}")
            return False
        
        return True
    
    def run_system_integration_test(self, mock_mt5):
        """运行系统集成测试"""
        print("\n🔄 运行系统集成测试（30秒）...")
        
        test_successful = True
        updates_count = 0
        
        def mock_trading_loop():
            nonlocal test_successful, updates_count
            
            try:
                with patch('hmm_auto_trader_24x7.mt5', mock_mt5):
                    start_time = time.time()
                    
                    while time.time() - start_time < 30 and updates_count < 5:  # 30秒或5次更新
                        try:
                            # 模拟获取最新K线
                            latest_candle = self.system.data_manager.get_latest_candle()
                            
                            if latest_candle:
                                # 稍微修改价格以模拟新K线
                                latest_candle['close'] += np.random.randn() * 0.1
                                latest_candle['datetime'] = datetime.now()
                                
                                # 生成信号
                                signal, reason = self.system.strategy_engine.generate_signal(latest_candle)
                                
                                # 执行交易（模拟模式）
                                if signal != SignalType.HOLD:
                                    trade_result = self.system.trade_executor.execute_signal(signal, reason)
                                    print(f"  📊 更新{updates_count+1}: {signal.name} - {'执行成功' if trade_result else '执行失败'}")
                                else:
                                    print(f"  📊 更新{updates_count+1}: HOLD - 无需交易")
                                
                                updates_count += 1
                            
                            time.sleep(6)  # 模拟实际更新间隔
                            
                        except Exception as e:
                            print(f"  ❌ 集成测试循环异常: {e}")
                            test_successful = False
                            break
                            
            except Exception as e:
                print(f"  ❌ 集成测试异常: {e}")
                test_successful = False
        
        # 启动测试线程
        test_thread = threading.Thread(target=mock_trading_loop, daemon=True)
        test_thread.start()
        test_thread.join(timeout=35)  # 最多等待35秒
        
        integration_success = test_successful and updates_count > 0
        self.test_results['integration_test'] = integration_success
        print(f"  ✅ 集成测试: {'成功' if integration_success else '失败'} - 处理了{updates_count}次更新")
        
        return integration_success
    
    def run_full_test(self):
        """运行完整测试套件"""
        print("🧪 开始HMM自动化交易系统试运行测试")
        print("=" * 80)
        
        try:
            # 设置模拟环境
            mock_mt5 = self.setup_mock_mt5()
            
            # 创建系统实例
            with patch('hmm_auto_trader_24x7.mt5', mock_mt5):
                self.system = HMMAutoTradingSystem(self.config)
                
                # 初始化系统（不启动主循环）
                init_result = self.system.initialize()
                if not init_result:
                    print("❌ 系统初始化失败")
                    return False
                
                print("✅ 系统初始化成功")
            
            # 运行各项测试
            test_functions = [
                ('连接管理', self.test_connection_management),
                ('数据管理', self.test_data_management),
                ('策略引擎', self.test_strategy_engine),
                ('信号生成', self.test_signal_generation),
                ('风险管理', self.test_risk_management),
                ('交易执行', self.test_trade_execution),
            ]
            
            all_passed = True
            for test_name, test_func in test_functions:
                try:
                    result = test_func(mock_mt5)
                    if not result:
                        all_passed = False
                        print(f"❌ {test_name}测试失败")
                except Exception as e:
                    all_passed = False
                    print(f"❌ {test_name}测试异常: {e}")
            
            # 运行集成测试
            if all_passed:
                integration_passed = self.run_system_integration_test(mock_mt5)
                all_passed = all_passed and integration_passed
            
            return self.generate_test_report(all_passed)
            
        except Exception as e:
            print(f"❌ 试运行测试失败: {e}")
            import traceback
            traceback.print_exc()
            return False
        
        finally:
            # 清理资源
            if self.system:
                try:
                    self.system.connection_manager.disconnect()
                except:
                    pass
    
    def generate_test_report(self, all_passed):
        """生成测试报告"""
        print("\n" + "=" * 80)
        print("📋 试运行测试报告")
        print("=" * 80)
        
        # 统计各类测试结果
        test_categories = [
            ('连接管理', 'connection_tests'),
            ('数据管理', 'data_tests'),
            ('策略引擎', 'strategy_tests'),
            ('信号生成', 'signal_tests'),
            ('风险管理', 'risk_tests'),
            ('交易执行', 'trade_tests')
        ]
        
        total_tests = 0
        passed_tests = 0
        
        for category_name, category_key in test_categories:
            if category_key in self.test_results:
                category_tests = self.test_results[category_key]
                category_total = len(category_tests)
                category_passed = sum(1 for _, result in category_tests if result)
                
                total_tests += category_total
                passed_tests += category_passed
                
                print(f"{category_name}: {category_passed}/{category_total} ({'✅' if category_passed == category_total else '❌'})")
                
                # 显示失败的测试
                for test_name, result in category_tests:
                    if not result:
                        print(f"  ❌ {test_name}")
        
        # 集成测试结果
        if 'integration_test' in self.test_results:
            integration_passed = self.test_results['integration_test']
            total_tests += 1
            if integration_passed:
                passed_tests += 1
            print(f"系统集成: {'✅ 通过' if integration_passed else '❌ 失败'}")
        
        # 总结
        success_rate = (passed_tests / total_tests * 100) if total_tests > 0 else 0
        print("\n" + "-" * 40)
        print(f"总测试数: {total_tests}")
        print(f"通过测试: {passed_tests}")
        print(f"成功率: {success_rate:.1f}%")
        
        if all_passed and success_rate >= 90:
            print("\n🎉 试运行测试全部通过！")
            print("系统已准备好进行模拟交易。")
            return True
        else:
            print("\n⚠️ 部分测试未通过，请检查系统配置。")
            return False

def main():
    """主函数"""
    try:
        tester = DryRunTester()
        success = tester.run_full_test()
        
        if success:
            print("\n" + "=" * 60)
            print("✅ 试运行测试完成，系统就绪！")
            print("\n🚀 准备进行模拟交易，请执行以下命令：")
            print("python run_trader.py --mode paper --symbol XAUUSD")
            print("\n💡 建议先运行几小时观察系统表现，确认稳定后再考虑实盘交易。")
        else:
            print("\n❌ 试运行测试未通过，请解决问题后重新测试。")
            sys.exit(1)
            
    except KeyboardInterrupt:
        print("\n👋 试运行测试被用户中断")
    except Exception as e:
        print(f"\n❌ 试运行测试异常: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main()