#!/usr/bin/env python3
"""
HMM自动化交易系统完整测试套件
分模块测试系统的各个功能组件
"""

import unittest
import pandas as pd
import numpy as np
from datetime import datetime, timezone, timedelta
import pytz
import json
import os
import sys
from unittest.mock import Mock, patch, MagicMock
import warnings
warnings.filterwarnings('ignore')

# 导入被测试模块
from config_loader import load_config, TradingConfig, TradingMode
from hmm_auto_trader_24x7 import (
    MT5ConnectionManager, DataManager, HMMStrategyEngine, 
    RiskManager, TradeExecutor, SignalType
)

class TestTimezoneHandling(unittest.TestCase):
    """测试时区处理功能"""
    
    def setUp(self):
        self.config = TradingConfig()
        self.logger = Mock()
        
    def test_timezone_configuration(self):
        """测试时区配置"""
        local_tz = pytz.timezone(self.config.local_timezone)
        server_tz = pytz.timezone(self.config.server_timezone)
        
        self.assertIsInstance(local_tz, pytz.tzfile.DstTzInfo)
        self.assertIsInstance(server_tz, pytz.tzfile.DstTzInfo)
        
        # 测试时区转换
        utc_time = datetime.now(timezone.utc)
        local_time = utc_time.astimezone(local_tz)
        server_time = utc_time.astimezone(server_tz)
        
        self.assertNotEqual(utc_time.hour, local_time.hour)  # 应该有时区差异
        print(f"✅ 时区配置测试通过: UTC={utc_time.strftime('%H:%M')}, Local={local_time.strftime('%H:%M')}, Server={server_time.strftime('%H:%M')}")
    
    def test_gmt_offset_calculation(self):
        """测试GMT偏移计算"""
        # 模拟服务器时间和UTC时间
        server_time = pd.Timestamp('2024-12-03 15:30:00')  # 假设服务器时间
        utc_time = pd.Timestamp('2024-12-03 12:30:00')     # 假设UTC时间
        
        gmt_offset = round((server_time - utc_time).total_seconds() / 3600)
        
        self.assertEqual(gmt_offset, 3)  # 应该是GMT+3
        print(f"✅ GMT偏移计算测试通过: {gmt_offset}小时")

class TestConfigLoader(unittest.TestCase):
    """测试配置加载器"""
    
    def test_default_config_loading(self):
        """测试默认配置加载"""
        config = TradingConfig()
        
        self.assertEqual(config.symbol, "XAUUSD")
        self.assertEqual(config.trading_mode, TradingMode.PAPER)
        self.assertEqual(config.n_states, 3)
        print("✅ 默认配置加载测试通过")
    
    def test_json_config_loading(self):
        """测试JSON配置文件加载"""
        # 创建临时配置文件
        test_config = {
            "trading": {"symbol": "EURUSD", "trading_mode": "live"},
            "strategy": {"n_states": 4},
            "risk_management": {"max_position_size": 0.02}
        }
        
        with open('test_config.json', 'w') as f:
            json.dump(test_config, f)
        
        try:
            config = load_config('test_config.json')
            self.assertEqual(config.symbol, "EURUSD")
            self.assertEqual(config.trading_mode, TradingMode.LIVE)
            self.assertEqual(config.n_states, 4)
            self.assertEqual(config.max_position_size, 0.02)
            print("✅ JSON配置加载测试通过")
        finally:
            if os.path.exists('test_config.json'):
                os.remove('test_config.json')

class TestMT5ConnectionManager(unittest.TestCase):
    """测试MT5连接管理器"""
    
    def setUp(self):
        self.config = TradingConfig()
        self.logger = Mock()
        self.conn_manager = MT5ConnectionManager(self.config, self.logger)
    
    @patch('hmm_auto_trader_24x7.mt5')
    def test_connection_initialization(self, mock_mt5):
        """测试连接初始化"""
        # 模拟成功连接
        mock_mt5.initialize.return_value = True
        mock_mt5.symbol_info.return_value = Mock(visible=True)
        mock_mt5.symbol_info_tick.return_value = Mock(time=**********)  # 模拟时间戳
        
        result = self.conn_manager.connect()
        
        mock_mt5.initialize.assert_called_once()
        self.assertTrue(result)
        self.assertTrue(self.conn_manager.connected)
        print("✅ MT5连接初始化测试通过")
    
    @patch('hmm_auto_trader_24x7.mt5')
    def test_heartbeat_check(self, mock_mt5):
        """测试心跳检测"""
        self.conn_manager.connected = True
        mock_mt5.account_info.return_value = Mock(balance=10000)
        
        result = self.conn_manager.heartbeat_check()
        
        self.assertTrue(result)
        mock_mt5.account_info.assert_called_once()
        print("✅ 心跳检测测试通过")

class TestDataManager(unittest.TestCase):
    """测试数据管理器"""
    
    def setUp(self):
        self.config = TradingConfig()
        self.logger = Mock()
        self.conn_manager = Mock()
        self.conn_manager.connected = True
        self.conn_manager.gmt_offset = 3
        self.data_manager = DataManager(self.config, self.conn_manager, self.logger)
    
    def test_feature_calculation(self):
        """测试技术特征计算"""
        # 创建模拟数据
        dates = pd.date_range('2024-12-01', periods=100, freq='H')
        np.random.seed(42)
        prices = 2000 + np.cumsum(np.random.randn(100) * 0.1)
        
        df = pd.DataFrame({
            'Open': prices + np.random.randn(100) * 0.05,
            'High': prices + abs(np.random.randn(100) * 0.1),
            'Low': prices - abs(np.random.randn(100) * 0.1),
            'Close': prices,
            'Volume': np.random.randint(1000, 5000, 100)
        }, index=dates)
        
        # 计算特征
        result_df = self.data_manager._calculate_features(df)
        
        # 验证特征存在
        expected_features = ['log_return', 'momentum_5m', 'momentum_20m', 'price_position', 'ma_diff']
        for feature in expected_features:
            self.assertIn(feature, result_df.columns)
        
        # 验证特征计算正确性
        self.assertFalse(result_df['log_return'].dropna().empty)
        self.assertFalse(result_df['ma_fast'].dropna().empty)
        self.assertFalse(result_df['ma_slow'].dropna().empty)
        
        print("✅ 技术特征计算测试通过")
    
    def test_data_update(self):
        """测试数据更新"""
        # 创建初始数据
        dates = pd.date_range('2024-12-01', periods=50, freq='H')
        prices = 2000 + np.cumsum(np.random.randn(50) * 0.1)
        
        self.data_manager.historical_data = pd.DataFrame({
            'Open': prices, 'High': prices + 1, 'Low': prices - 1, 
            'Close': prices, 'Volume': 1000
        }, index=dates)
        
        # 计算初始特征
        self.data_manager.historical_data = self.data_manager._calculate_features(
            self.data_manager.historical_data
        )
        
        initial_length = len(self.data_manager.historical_data)
        
        # 模拟新K线数据
        new_candle = {
            'datetime': dates[-1] + timedelta(hours=1),
            'open': prices[-1] + 0.5,
            'high': prices[-1] + 1.0,
            'low': prices[-1] - 0.5,
            'close': prices[-1] + 0.3,
            'volume': 1200
        }
        
        result = self.data_manager.update_historical_data(new_candle)
        
        self.assertTrue(result)
        self.assertEqual(len(self.data_manager.historical_data), initial_length + 1)
        print("✅ 数据更新测试通过")

class TestHMMStrategyEngine(unittest.TestCase):
    """测试HMM策略引擎"""
    
    def setUp(self):
        self.config = TradingConfig()
        self.logger = Mock()
        self.data_manager = Mock()
        self.strategy_engine = HMMStrategyEngine(self.config, self.data_manager, self.logger)
        
        # 创建模拟历史数据
        self.mock_historical_data = self._create_mock_data()
        self.data_manager.historical_data = self.mock_historical_data
    
    def _create_mock_data(self):
        """创建模拟历史数据"""
        np.random.seed(42)
        dates = pd.date_range('2024-11-01', periods=1000, freq='H')
        prices = 2000 + np.cumsum(np.random.randn(1000) * 0.5)
        
        df = pd.DataFrame({
            'Open': prices + np.random.randn(1000) * 0.1,
            'High': prices + abs(np.random.randn(1000) * 0.2),
            'Low': prices - abs(np.random.randn(1000) * 0.2),
            'Close': prices,
            'Volume': np.random.randint(1000, 5000, 1000)
        }, index=dates)
        
        # 计算技术特征
        df['log_return'] = np.log(df['Close'] / df['Close'].shift(1))
        df['momentum_5m'] = np.log(df['Close'] / df['Close'].shift(5))
        df['momentum_20m'] = np.log(df['Close'] / df['Close'].shift(20))
        df['ma_fast'] = df['Close'].rolling(window=20).mean()
        df['ma_slow'] = df['Close'].rolling(window=35).mean()
        df['price_position'] = (df['Close'] - df['ma_slow']) / df['ma_slow']
        df['ma_diff'] = (df['ma_fast'] - df['ma_slow']) / df['ma_slow']
        
        return df.dropna()
    
    def test_walk_forward_analysis(self):
        """测试前向展开分析 - 对比原始逻辑"""
        result = self.strategy_engine.walk_forward_analysis()
        
        self.assertTrue(result)
        self.assertIn('train_ratio', self.strategy_engine.best_params)
        self.assertIn('sharpe_ratio', self.strategy_engine.best_params)
        
        # 验证训练比例在合理范围内
        train_ratio = self.strategy_engine.best_params['train_ratio']
        self.assertGreaterEqual(train_ratio, 0.6)
        self.assertLessEqual(train_ratio, 0.8)
        
        print(f"✅ 前向展开分析测试通过 - 最佳训练比例: {train_ratio:.2f}")
    
    def test_model_training(self):
        """测试模型训练"""
        # 先运行前向分析
        self.strategy_engine.walk_forward_analysis()
        
        # 训练模型
        result = self.strategy_engine.train_model()
        
        self.assertTrue(result)
        self.assertTrue(self.strategy_engine.is_trained)
        self.assertIsNotNone(self.strategy_engine.model)
        self.assertIsNotNone(self.strategy_engine.scaler)
        self.assertTrue(len(self.strategy_engine.state_map) > 0)
        
        print(f"✅ 模型训练测试通过 - 状态映射: {self.strategy_engine.state_map}")
    
    def test_signal_generation_logic(self):
        """测试信号生成逻辑 - 确保与原始代码一致"""
        # 训练模型
        self.strategy_engine.walk_forward_analysis()
        self.strategy_engine.train_model()
        
        # 模拟新K线数据
        last_candle = self.mock_historical_data.iloc[-1]
        new_candle = {
            'datetime': last_candle.name + timedelta(hours=1),
            'open': last_candle['Close'] + 0.1,
            'high': last_candle['Close'] + 0.5,
            'low': last_candle['Close'] - 0.3,
            'close': last_candle['Close'] + 0.2,
            'volume': 1500
        }
        
        # 生成信号
        signal, reason = self.strategy_engine.generate_signal(new_candle)
        
        self.assertIsInstance(signal, SignalType)
        self.assertIn(signal, [SignalType.BUY, SignalType.SELL, SignalType.HOLD])
        self.assertTrue(isinstance(reason, str) and len(reason) > 0)
        
        print(f"✅ 信号生成逻辑测试通过 - 信号: {signal.name}, 原因: {reason}")

class TestRiskManager(unittest.TestCase):
    """测试风险管理器"""
    
    def setUp(self):
        self.config = TradingConfig()
        self.logger = Mock()
        self.conn_manager = Mock()
        self.risk_manager = RiskManager(self.config, self.conn_manager, self.logger)
    
    @patch('hmm_auto_trader_24x7.mt5')
    def test_can_trade_checks(self, mock_mt5):
        """测试交易权限检查"""
        self.conn_manager.connected = True
        
        # 模拟正常账户状态
        mock_account = Mock()
        mock_account.equity = 10000
        mock_account.balance = 10000
        mock_account.margin = 1000
        mock_account.margin_level = 1000
        mock_mt5.account_info.return_value = mock_account
        
        can_trade, reason = self.risk_manager.can_trade()
        
        self.assertTrue(can_trade)
        self.assertEqual(reason, "风险检查通过")
        print("✅ 交易权限检查测试通过")
    
    @patch('hmm_auto_trader_24x7.mt5')
    def test_position_size_calculation(self, mock_mt5):
        """测试仓位大小计算"""
        # 模拟账户和品种信息
        mock_account = Mock()
        mock_account.balance = 10000
        mock_mt5.account_info.return_value = mock_account
        
        mock_symbol = Mock()
        mock_symbol.trade_contract_size = 100
        mock_symbol.point = 0.01
        mock_symbol.volume_min = 0.01
        mock_symbol.volume_step = 0.01
        mock_mt5.symbol_info.return_value = mock_symbol
        
        position_size = self.risk_manager.calculate_position_size(SignalType.BUY)
        
        self.assertGreater(position_size, 0)
        self.assertLessEqual(position_size, self.config.max_position_size)
        print(f"✅ 仓位计算测试通过 - 计算仓位: {position_size}")

class TestTradeExecutor(unittest.TestCase):
    """测试交易执行器"""
    
    def setUp(self):
        self.config = TradingConfig()
        self.config.trading_mode = TradingMode.PAPER  # 使用模拟模式测试
        self.logger = Mock()
        self.conn_manager = Mock()
        self.risk_manager = Mock()
        self.trade_executor = TradeExecutor(self.config, self.conn_manager, self.risk_manager, self.logger)
    
    @patch('hmm_auto_trader_24x7.mt5')
    def test_signal_execution_paper_mode(self, mock_mt5):
        """测试模拟模式下的信号执行"""
        # 设置风险管理器返回值
        self.risk_manager.can_trade.return_value = (True, "风险检查通过")
        self.risk_manager.calculate_position_size.return_value = 0.01
        
        # 模拟无持仓状态
        mock_mt5.positions_get.return_value = []
        
        # 模拟报价
        mock_tick = Mock()
        mock_tick.ask = 2050.0
        mock_tick.bid = 2049.0
        mock_mt5.symbol_info_tick.return_value = mock_tick
        
        # 执行买入信号
        result = self.trade_executor.execute_signal(SignalType.BUY, "测试买入信号")
        
        self.assertTrue(result)
        self.risk_manager.can_trade.assert_called_once()
        print("✅ 模拟模式交易执行测试通过")

class TestSystemIntegration(unittest.TestCase):
    """系统集成测试"""
    
    def setUp(self):
        self.config = TradingConfig()
        self.config.trading_mode = TradingMode.PAPER
        
    @patch('hmm_auto_trader_24x7.mt5')
    def test_system_initialization_flow(self, mock_mt5):
        """测试系统初始化流程"""
        from hmm_auto_trader_24x7 import HMMAutoTradingSystem
        
        # 模拟MT5连接成功
        mock_mt5.initialize.return_value = True
        mock_mt5.symbol_info.return_value = Mock(visible=True)
        mock_mt5.symbol_info_tick.return_value = Mock(time=**********)
        mock_mt5.account_info.return_value = Mock(login=12345, balance=10000)
        
        # 模拟历史数据
        np.random.seed(42)
        mock_rates = []
        base_time = **********
        base_price = 2000
        
        for i in range(2000):  # 提供足够的数据
            price = base_price + np.random.randn() * 0.5
            mock_rates.append({
                'time': base_time + i * 60,
                'open': price + np.random.randn() * 0.1,
                'high': price + abs(np.random.randn() * 0.2),
                'low': price - abs(np.random.randn() * 0.2),
                'close': price,
                'tick_volume': np.random.randint(100, 500)
            })
        
        mock_mt5.copy_rates_range.return_value = np.array(mock_rates, dtype=object)
        
        # 创建系统实例
        system = HMMAutoTradingSystem(self.config)
        
        # 测试初始化
        result = system.initialize()
        
        self.assertTrue(result)
        self.assertTrue(system.connection_manager.connected)
        self.assertTrue(system.strategy_engine.is_trained)
        print("✅ 系统集成初始化测试通过")

class TestOriginalStrategyConsistency(unittest.TestCase):
    """测试与原始策略的一致性"""
    
    def test_feature_calculation_consistency(self):
        """测试特征计算与原始代码的一致性"""
        # 创建相同的测试数据
        np.random.seed(42)
        dates = pd.date_range('2024-11-01', periods=100, freq='H')
        prices = 2000 + np.cumsum(np.random.randn(100) * 0.1)
        
        df = pd.DataFrame({
            'Close': prices
        }, index=dates)
        
        # 使用新系统的特征计算
        config = TradingConfig()
        data_manager = DataManager(config, Mock(), Mock())
        
        # 添加OHLCV数据
        df['Open'] = df['Close'] + np.random.randn(100) * 0.05
        df['High'] = df['Close'] + abs(np.random.randn(100) * 0.1)
        df['Low'] = df['Close'] - abs(np.random.randn(100) * 0.1)
        df['Volume'] = np.random.randint(1000, 5000, 100)
        
        result_df = data_manager._calculate_features(df)
        
        # 手工计算相同特征（模拟原始代码）
        expected_df = df.copy()
        expected_df['log_return'] = np.log(expected_df['Close'] / expected_df['Close'].shift(1))
        expected_df['momentum_5m'] = np.log(expected_df['Close'] / expected_df['Close'].shift(5))
        expected_df['momentum_20m'] = np.log(expected_df['Close'] / expected_df['Close'].shift(20))
        expected_df['ma_fast'] = expected_df['Close'].rolling(window=20).mean()
        expected_df['ma_slow'] = expected_df['Close'].rolling(window=35).mean()
        expected_df['price_position'] = (expected_df['Close'] - expected_df['ma_slow']) / expected_df['ma_slow']
        expected_df['ma_diff'] = (expected_df['ma_fast'] - expected_df['ma_slow']) / expected_df['ma_slow']
        
        # 比较结果（忽略NaN值）
        features = ['log_return', 'momentum_5m', 'momentum_20m', 'price_position', 'ma_diff']
        for feature in features:
            result_values = result_df[feature].dropna()
            expected_values = expected_df[feature].dropna()
            
            if len(result_values) > 0 and len(expected_values) > 0:
                # 比较非NaN值
                common_indices = result_values.index.intersection(expected_values.index)
                if len(common_indices) > 0:
                    np.testing.assert_array_almost_equal(
                        result_values.loc[common_indices].values,
                        expected_values.loc[common_indices].values,
                        decimal=10
                    )
        
        print("✅ 特征计算一致性测试通过")
    
    def test_signal_generation_logic_consistency(self):
        """测试信号生成逻辑与原始代码的一致性"""
        # 核心逻辑：
        # 1. 使用shift(1)避免未来信息泄露
        # 2. 状态映射："上涨"->1, "下跌"->-1, "盘整"->0
        # 3. 信号基于前一个状态，而不是当前状态
        
        # 模拟状态序列
        regimes = pd.Series(['下跌', '下跌', '上涨', '上涨', '盘整', '盘整', '下跌'])
        
        # 原始逻辑
        original_signals = regimes.map({"上涨": 1, "下跌": -1, "盘整": 0}).shift(1).fillna(0)
        
        # 新系统逻辑（应该相同）
        new_signals = regimes.map({"上涨": 1, "下跌": -1, "盘整": 0}).shift(1).fillna(0)
        
        pd.testing.assert_series_equal(original_signals, new_signals)
        
        # 验证关键特性：信号基于前一个状态
        self.assertEqual(original_signals.iloc[1], -1)  # 第二个信号基于第一个状态（下跌）
        self.assertEqual(original_signals.iloc[2], -1)  # 第三个信号基于第二个状态（下跌）
        self.assertEqual(original_signals.iloc[3], 1)   # 第四个信号基于第三个状态（上涨）
        
        print("✅ 信号生成逻辑一致性测试通过")

def run_all_tests():
    """运行所有测试"""
    print("🧪 开始运行HMM自动化交易系统测试套件")
    print("=" * 80)
    
    # 创建测试套件
    test_classes = [
        TestTimezoneHandling,
        TestConfigLoader,
        TestMT5ConnectionManager,
        TestDataManager,
        TestHMMStrategyEngine,
        TestRiskManager,
        TestTradeExecutor,
        TestOriginalStrategyConsistency,
        TestSystemIntegration
    ]
    
    suite = unittest.TestSuite()
    for test_class in test_classes:
        tests = unittest.TestLoader().loadTestsFromTestCase(test_class)
        suite.addTests(tests)
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    print("\n" + "=" * 80)
    print("🧪 测试套件运行完成")
    print(f"总测试数: {result.testsRun}")
    print(f"成功: {result.testsRun - len(result.failures) - len(result.errors)}")
    print(f"失败: {len(result.failures)}")
    print(f"错误: {len(result.errors)}")
    
    if result.failures:
        print("\n❌ 失败的测试:")
        for test, traceback in result.failures:
            print(f"  - {test}: {traceback}")
    
    if result.errors:
        print("\n❌ 错误的测试:")
        for test, traceback in result.errors:
            print(f"  - {test}: {traceback}")
    
    success_rate = (result.testsRun - len(result.failures) - len(result.errors)) / result.testsRun * 100
    print(f"\n🎯 测试通过率: {success_rate:.1f}%")
    
    return result.wasSuccessful()

if __name__ == "__main__":
    success = run_all_tests()
    
    if success:
        print("\n✅ 所有测试通过！系统可以进行试运行。")
    else:
        print("\n❌ 部分测试失败，请检查系统配置。")
        sys.exit(1)